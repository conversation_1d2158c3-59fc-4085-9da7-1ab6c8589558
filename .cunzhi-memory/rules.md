# 开发规范和规则

- 巨量指数功能开发规则：1.保存按钮只需完善函数框架，具体逻辑留空；2.地区选择需新增手动输入城市列表按钮；3.账号管理弹窗先实现添加/删除功能；4.采集名称和地区类型都是单选；5.数据聚合方式和处理周期必须选择；6.开始按钮遇错变继续按钮；7.开始和停止按钮逻辑不实现
- 不要生成总结性Markdown文档，不要生成测试脚本，不要编译代码，不要运行代码，用户会自己处理这些操作
- JLZS模块Puppeteer错误修复：TargetClosedException(setDeviceMetricsOverride)错误通过改用Puppeteer原生API控制浏览器窗口可见性来解决，避免使用JavaScript的window操作导致的WebSocket连接问题
- 用户要求不要生成总结性Markdown文档、不要生成测试脚本、不要编译、不要运行，用户会自己处理这些操作
- 用户明确要求移除所有浏览器隐藏/显示测试功能，保持程序纯粹的账号管理和登录功能。已完成清理：删除JlzsBrowserManager中的测试浏览器功能、AccountManageDialog中的测试按钮、JlzsLoginManager中的隐藏功能，编译成功无错误。
- 用户明确要求：不要生成总结性Markdown文档，不要生成测试脚本，需要帮助编译，不要运行代码（用户自己运行）
- 用户明确要求：不要生成总结性Markdown文档，不要生成测试脚本，需要帮助编译，不要运行代码（用户自己运行）
- 用户要求取消页面导航，因为网站会自己导航。不要生成总结性Markdown文档，不要生成测试脚本，需要帮助编译，但不要运行（用户自己运行）
- 巨量指数代理IP配置优化已完成：1.修复代理认证方式，改用page.authenticate()方法而非启动参数；2.添加代理有效时间配置（分钟单位）；3.实现msToken自动提取功能，登录成功后从document.cookie中获取并关联账号；4.扩展JlzsProxyConfig模型，添加validTime和startTime字段；5.完善代理配置UI，添加有效时间输入框、代理类型说明和自动获取按钮；6.编译成功无错误
- 巨量指数基础功能修复完成：1.禁用账号功能修复-添加状态更新和UI刷新，正确关闭浏览器实例；2.代理测试功能完善-多URL测试提高成功率，基础格式验证，过期检查；3.代理自动获取功能框架-UI集成，API调用框架，配置管理；4.msToken管理功能-有效期检查，批量验证，自动刷新机制；5.编译成功无错误，所有功能已集成到JlzsLogic中
- 巨量指数完整功能优化已完成：1.代理认证修复-改用page.authenticate()方法；2.msToken自动提取-登录成功后从document.cookie获取并关联账号；3.代理配置扩展-添加有效时间、过期检查、显示信息等功能；4.禁用账号功能修复-正确关闭浏览器实例并更新UI状态；5.代理测试功能-多URL测试提高成功率；6.代理自动获取框架-UI集成和API调用准备；7.msToken管理功能-有效期检查、批量验证、自动刷新；8.UI界面完善-添加msToken状态显示、批量操作按钮、代理类型说明；9.编译成功无错误
- 巨量指数代理验证修复完成：参考百度指数的checkProxy方法，使用HttpClient而非Dio进行代理验证，支持用户名密码认证格式'PROXY username:password@address:port'，测试连接https://www.baidu.com，10秒超时，编译成功无错误。代理验证现在应该能正确处理51代理等付费代理服务
- 巨量指数登录流程中存在浏览器断开连接导致页面导航失败的问题，需要修复Navigation failed because browser has disconnected异常
- 巨量指数开始功能验证规则：1.勾选全国时开始日期只能从2019-01-01开始，勾选省份或城市则从2022-06-04开始；2.地区类型选择合并查询时，勾选全国则不能勾选其他地区；3.必须验证关键词列表、时间范围、地区选择、采集平台、地区类型等必填项；4.已在docs目录创建开始按钮基础功能需求文档
- JLZS API拦截器需要支持arithmetic-index分析页面的API监听，不仅仅是主页面和登录页面
- JLZS API拦截需要改为全局浏览器级别拦截，不依赖单个页面，因为页面会被关闭导致拦截失效
- JLZS API拦截器需要添加cookies提取功能，参考百度指数实现，在API响应时提取特定cookies
- JLZS API拦截器虽然设置成功但页面被关闭导致拦截失效，需要解决页面生命周期管理问题
- JLZS简化版本成功解决浏览器连接断开问题，现在需要等待用户手动完成登录后测试API拦截
- JLZS登录成功后需要自动跳转到分析页面以触发API拦截测试
- JLZS登录成功后浏览器连接断开，需要检查是否有代码在登录成功后关闭浏览器
- JLZS登录成功回调触发太快导致浏览器连接未稳定，需要添加延迟等待连接稳定
- JLZS浏览器连接断开问题修复完成：1.添加连接保活机制-每10秒检查浏览器连接状态，连接断开时自动停止保活；2.改进连接检查逻辑-先检查browser.version再检查browser.pages，增加超时控制；3.添加重连机制-连接断开时自动重新创建浏览器实例并恢复代理配置；4.优化资源管理-dispose时停止所有保活定时器，避免内存泄漏；5.编译成功无错误
- JLZS cookies拼接功能已完成：在jlzs_api_interceptor.dart中修改_extractCookiesFromResponse方法，添加cookieStrings列表收集所有cookies的"name=value"格式，使用join('; ')拼接成完整Cookie字符串，保存到account.session.userInfo['fullCookieString']中，编译成功无错误
- JLZS API客户端Cookie使用优化完成：修改jlzs_api_client.dart中的executeTask方法，优先使用保存的完整Cookie字符串(account.session.userInfo['fullCookieString'])，添加从Cookie字符串提取msToken的静态方法，实现多层备用方案(完整Cookie字符串->实时Cookie->存储session)，所有相关方法改为静态方法以支持静态调用，编译通过无语法错误
- JLZS连接保活超时问题修复完成：将jlzs_login_manager_new.dart中_startKeepAlive方法的browser.version超时时间从3秒增加到10秒，解决代理网络延迟导致的TimeoutException问题，编译成功无错误
- JLZS连接保活机制优化完成：改进jlzs_login_manager_new.dart中的连接检查方式，使用多层轻量级检查(targets->version->pages)，降低检查频率到15秒，修复browser.targets.timeout编译错误，增强连接稳定性检测，编译成功无错误
- 用户明确要求：先建立需求文档放在docs目录下，然后等待进一步指令。不要生成总结性Markdown文档，不要生成测试脚本，需要帮助编译，但不要运行（用户自己运行）
- 用户要求按照需求文档实现巨量指数数据保存的所有功能，包括分开查询和合并查询数据保存。不要生成总结性Markdown文档，不要生成测试脚本，需要帮助编译，但不要运行（用户自己运行）
- 用户测试发现数据保存功能有问题：关键词是日本，地区是安徽，日期范围是20250614-20250712，但保存的CSV文件只有一条数据且日期、综合指数、搜索指数都不正确。需要修复数据保存逻辑。不要生成总结性Markdown文档，不要生成测试脚本，需要帮助编译，但不要运行（用户自己运行）
- 用户测试发现三个问题：1.搜索指数一直是0但API有值；2.相同任务保存的WPS文件不会覆盖；3.点击开始时旧数据也被保存。需要修复这些问题。不要生成总结性Markdown文档，不要生成测试脚本，需要帮助编译，但不要运行（用户自己运行）
- 用户发现搜索指数仍然是0，提供了多关键词JSON示例。发现每个关键词都有自己的search_hot_list，不是全局的。需要修复搜索指数提取逻辑。不要生成总结性Markdown文档，不要生成测试脚本，需要帮助编译，但不要运行（用户自己运行）
- 百度指数页面组件拆分规则：1.创建文档记录项目细节到docs/issues目录；2.先分析需求并结合现有代码规划计划；3.逐步实现，每实现一个步骤更新文档进度；4.实现过程中查看每个不了解的API防止误会；5.随时使用zhi与用户沟通防止信息概念不同步；6.更新文档前需完整阅读文档内容，理解更新目的和需求，列出需要更新的点，然后逐步更新以防遗忘
- 百度指数性能优化分析结果：主要卡顿原因是频繁日志输出导致UI更新(60-70%)和任务收集算法同步执行(20-25%)，需要实施批量日志输出、任务收集缓存、优化倒计时显示三个优化方案
