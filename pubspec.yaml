name: bd
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.4.0 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6


dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^3.0.0
  calendar_date_picker2: ^1.0.2
  http: ^1.2.1
  dio: ^5.7.0
  pretty_dio_logger: ^1.3.1
  dio5_log : ^5.0.0
  csv: ^6.0.0
  intl: ^0.19.0
  oktoast: ^3.4.0
  get: ^4.6.6
  file_picker: ^8.0.5
  excel: ^2.0.0
  encrypt: ^5.0.0
  path: ^1.8.0
  window_manager: ^0.4.2
  tekflat_design: 1.6.12-flutter3.22
  dotted_line: ^3.2.2
  puppeteer: ^3.15.0
  local_notifier: ^0.1.5
  native_dio_adapter: ^1.3.0
  desktop_multi_window: ^0.2.1
  ffi: ^2.1.0
  win32: ^5.5.0
  archive: ^3.4.10  # ZIP文件压缩
  mailer: ^6.1.0    # 邮件发送功能
  path_provider: ^2.1.2  # 获取系统路径

  # 键值数据库  [NoSQL 数据库]
#  hive: ^2.2.3
# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
     - assets/cityShip.json
     - assets/provinces.json
     - assets/jlzs_provinces_cities.json

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  ## 配置字体资源
    #  fonts:
    #   - family: RubikMonoOne
    #     fonts:
    #      - asset: fonts/RubikMonoOne-Regular.ttf example:


  # 配置字体资源
  fonts:
    - family: RubikMonoOne
      fonts:
        - asset: assets/fonts/Alibaba-PuHuiTi-Bold.ttf
        - asset: assets/fonts/Alibaba-PuHuiTi-Heavy.ttf
        - asset: assets/fonts/Alibaba-PuHuiTi-Light.ttf
        - asset: assets/fonts/Alibaba-PuHuiTi-Medium.ttf
        - asset: assets/fonts/Alibaba-PuHuiTi-Regular.ttf

#          style: italic
#     - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
