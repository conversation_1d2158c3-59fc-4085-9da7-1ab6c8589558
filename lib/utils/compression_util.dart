import 'dart:io';
import 'package:archive/archive.dart';
import 'package:path/path.dart' as path;

/// 文件压缩工具类
/// 提供ZIP文件压缩功能
class CompressionUtil {
  CompressionUtil._();

  /// 压缩整个文件夹为ZIP文件
  /// 
  /// [sourceFolderPath] 源文件夹路径
  /// [outputZipPath] 输出ZIP文件路径
  /// [onProgress] 进度回调函数，参数为当前处理的文件名
  /// 
  /// 返回是否压缩成功
  static Future<bool> compressFolderToZip({
    required String sourceFolderPath,
    required String outputZipPath,
    Function(String fileName)? onProgress,
  }) async {
    try {
      // 检查源文件夹是否存在
      final sourceDir = Directory(sourceFolderPath);
      if (!await sourceDir.exists()) {
        print('错误：源文件夹不存在: $sourceFolderPath');
        return false;
      }

      // 创建Archive对象
      final archive = Archive();

      // 递归添加文件夹中的所有文件
      await _addDirectoryToArchive(
        archive, 
        sourceDir, 
        sourceDir.path,
        onProgress,
      );

      // 编码为ZIP格式
      final zipData = ZipEncoder().encode(archive);
      if (zipData == null) {
        print('错误：ZIP编码失败');
        return false;
      }

      // 写入ZIP文件
      final outputFile = File(outputZipPath);
      await outputFile.writeAsBytes(zipData);

      print('✅ 压缩完成: $outputZipPath');
      final fileSize = await outputFile.length();
      print('📦 压缩文件大小: ${await _formatFileSize(fileSize)}');
      
      return true;
    } catch (e) {
      print('❌ 压缩失败: $e');
      return false;
    }
  }

  /// 递归添加目录中的文件到Archive
  static Future<void> _addDirectoryToArchive(
    Archive archive,
    Directory directory,
    String basePath,
    Function(String fileName)? onProgress,
  ) async {
    await for (final entity in directory.list(recursive: false)) {
      if (entity is File) {
        // 添加文件
        final relativePath = path.relative(entity.path, from: basePath);
        final fileBytes = await entity.readAsBytes();
        
        final archiveFile = ArchiveFile(
          relativePath.replaceAll('\\', '/'), // 统一使用正斜杠
          fileBytes.length,
          fileBytes,
        );
        
        archive.addFile(archiveFile);
        
        // 调用进度回调
        onProgress?.call(path.basename(entity.path));
        
      } else if (entity is Directory) {
        // 递归处理子目录
        await _addDirectoryToArchive(
          archive,
          entity,
          basePath,
          onProgress,
        );
      }
    }
  }

  /// 格式化文件大小显示
  static Future<String> _formatFileSize(int bytes) async {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(2)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(2)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(2)} GB';
  }

  /// 获取文件夹大小（用于压缩前的估算）
  static Future<int> getFolderSize(String folderPath) async {
    try {
      final directory = Directory(folderPath);
      if (!await directory.exists()) return 0;

      int totalSize = 0;
      await for (final entity in directory.list(recursive: true)) {
        if (entity is File) {
          totalSize += await entity.length();
        }
      }
      return totalSize;
    } catch (e) {
      print('获取文件夹大小失败: $e');
      return 0;
    }
  }

  /// 生成压缩文件名
  /// 
  /// [timestamp] 时间戳文件夹名
  /// [prefix] 文件名前缀，默认为"百度指数数据"
  /// 
  /// 返回格式: "百度指数数据_2025-01-15_14-30-25.zip"
  static String generateZipFileName(String timestamp, {String prefix = "百度指数数据"}) {
    return "${prefix}_$timestamp.zip";
  }

  /// 检查ZIP文件是否有效
  static Future<bool> isValidZipFile(String zipPath) async {
    try {
      final file = File(zipPath);
      if (!await file.exists()) return false;

      final bytes = await file.readAsBytes();
      final archive = ZipDecoder().decodeBytes(bytes);
      
      return archive.isNotEmpty;
    } catch (e) {
      print('ZIP文件验证失败: $e');
      return false;
    }
  }

  /// 获取ZIP文件信息
  static Future<Map<String, dynamic>> getZipInfo(String zipPath) async {
    try {
      final file = File(zipPath);
      if (!await file.exists()) {
        return {'error': '文件不存在'};
      }

      final bytes = await file.readAsBytes();
      final archive = ZipDecoder().decodeBytes(bytes);
      
      int totalFiles = archive.length;
      int totalSize = archive.fold(0, (sum, file) => sum + file.size);
      int compressedSize = await file.length();
      
      return {
        'fileName': path.basename(zipPath),
        'filePath': zipPath,
        'fileCount': totalFiles,
        'originalSize': await _formatFileSize(totalSize),
        'compressedSize': await _formatFileSize(compressedSize),
        'compressionRatio': totalSize > 0 ? 
          '${((1 - compressedSize / totalSize) * 100).toStringAsFixed(1)}%' : '0%',
      };
    } catch (e) {
      return {'error': '获取ZIP信息失败: $e'};
    }
  }
}
