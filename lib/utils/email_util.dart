import 'dart:io';
import 'package:mailer/mailer.dart';
import 'package:mailer/smtp_server.dart';
import 'package:path/path.dart' as path;

/// 邮件发送工具类
/// 提供邮件发送功能，支持附件
class EmailUtil {
  EmailUtil._();

  // 固定的邮件配置（根据用户要求）
  static const String _senderEmail = '<EMAIL>';  // 替换为实际发件人邮箱
  static const String _senderPassword = 'mcb15655615470..';    // 替换为实际应用密码
  static const String _smtpHost = 'smtp.163.com';          // 163邮箱SMTP服务器
  static const int _smtpPort = 465;                             // 163邮箱SSL端口
  static const bool _useSSL = true;                             // 163邮箱使用SSL
  static const bool _useTLS = false;                            // SSL模式下不需要TLS

  /// 发送带附件的邮件
  /// 
  /// [recipientEmail] 收件人邮箱
  /// [subject] 邮件主题
  /// [body] 邮件正文
  /// [attachmentPath] 附件文件路径
  /// [onProgress] 进度回调函数
  /// 
  /// 返回是否发送成功
  static Future<bool> sendEmailWithAttachment({
    required String recipientEmail,
    required String subject,
    required String body,
    required String attachmentPath,
    Function(String status)? onProgress,
  }) async {
    try {
      onProgress?.call('正在准备邮件...');

      // 验证收件人邮箱格式
      if (!_isValidEmail(recipientEmail)) {
        print('❌ 无效的收件人邮箱格式: $recipientEmail');
        return false;
      }

      // 检查附件文件是否存在
      final attachmentFile = File(attachmentPath);
      if (!await attachmentFile.exists()) {
        print('❌ 附件文件不存在: $attachmentPath');
        return false;
      }

      onProgress?.call('正在连接邮件服务器...');

      // 配置SMTP服务器 - 163邮箱专用配置
      final smtpServer = SmtpServer(
        _smtpHost,
        port: _smtpPort,
        ssl: _useSSL,
        allowInsecure: true,
        username: _senderEmail,
        password: _senderPassword,
        ignoreBadCertificate: true,  // 忽略证书错误
      );

      // 创建邮件消息
      final message = Message()
        ..from = Address(_senderEmail, '百度指数数据系统')
        ..recipients.add(recipientEmail)
        ..subject = subject
        ..html = _formatEmailBody(body);

      onProgress?.call('正在添加附件...');

      // 添加附件
      final fileName = path.basename(attachmentPath);

      message.attachments.add(FileAttachment(
        attachmentFile,
      ));

      onProgress?.call('正在发送邮件...');

      // 发送邮件 - 添加重试机制
      int maxRetries = 3;
      for (int attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          onProgress?.call('正在发送邮件... (尝试 $attempt/$maxRetries)');

          final sendReport = await send(message, smtpServer);

          // 检查发送报告
          print('✅ 邮件发送成功');
          print('📧 收件人: $recipientEmail');
          print('📎 附件: $fileName');
          print('📋 发送报告: $sendReport');
          onProgress?.call('邮件发送成功');
          return true;

        } catch (e) {
          print('❌ 邮件发送失败 (尝试 $attempt/$maxRetries): $e');

          if (attempt == maxRetries) {
            // 最后一次尝试失败，抛出异常
            rethrow;
          } else {
            // 等待后重试
            onProgress?.call('发送失败，等待重试...');
            await Future.delayed(Duration(seconds: 5));
          }
        }
      }

      return false;

    } catch (e) {
      print('❌ 邮件发送异常: $e');
      onProgress?.call('邮件发送失败: $e');
      return false;
    }
  }

  /// 发送百度指数数据邮件
  /// 
  /// [recipientEmail] 收件人邮箱
  /// [zipFilePath] ZIP文件路径
  /// [timestamp] 时间戳
  /// [keywordCount] 关键词数量
  /// [regionCount] 地区数量
  /// [onProgress] 进度回调
  static Future<bool> sendBaiduIndexDataEmail({
    required String recipientEmail,
    required String zipFilePath,
    required String timestamp,
    int keywordCount = 0,
    int regionCount = 0,
    Function(String status)? onProgress,
  }) async {
    // 生成邮件主题
    final subject = '百度指数数据报告 - $timestamp';
    
    // 生成邮件正文
    final body = _generateBaiduIndexEmailBody(
      timestamp: timestamp,
      keywordCount: keywordCount,
      regionCount: regionCount,
      zipFileName: path.basename(zipFilePath),
    );

    return await sendEmailWithAttachment(
      recipientEmail: recipientEmail,
      subject: subject,
      body: body,
      attachmentPath: zipFilePath,
      onProgress: onProgress,
    );
  }

  /// 验证邮箱格式
  static bool _isValidEmail(String email) {
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    return emailRegex.hasMatch(email);
  }

  /// 格式化邮件正文为HTML
  static String _formatEmailBody(String body) {
    return '''
    <html>
      <head>
        <meta charset="UTF-8">
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .header { background-color: #f4f4f4; padding: 20px; text-align: center; }
          .content { padding: 20px; }
          .footer { background-color: #f4f4f4; padding: 10px; text-align: center; font-size: 12px; color: #666; }
        </style>
      </head>
      <body>
        <div class="header">
          <h2>百度指数数据系统</h2>
        </div>
        <div class="content">
          $body
        </div>
        <div class="footer">
          <p>此邮件由百度指数数据系统自动发送，请勿回复。</p>
          <p>发送时间: ${DateTime.now().toString()}</p>
        </div>
      </body>
    </html>
    ''';
  }

  /// 生成百度指数数据邮件正文
  static String _generateBaiduIndexEmailBody({
    required String timestamp,
    required int keywordCount,
    required int regionCount,
    required String zipFileName,
  }) {
    return '''
    <h3>📊 百度指数数据采集完成</h3>
    
    <p>您好！</p>
    
    <p>百度指数数据采集任务已成功完成，详细信息如下：</p>
    
    <ul>
      <li><strong>采集时间：</strong> $timestamp</li>
      <li><strong>关键词数量：</strong> $keywordCount 个</li>
      <li><strong>地区数量：</strong> $regionCount 个</li>
      <li><strong>数据文件：</strong> $zipFileName</li>
    </ul>
    
    <h4>📁 数据包内容：</h4>
    <ul>
      <li><strong>原始数据：</strong> 按地区分类的CSV文件（支持日/周/月/年维度）</li>
      <li><strong>合并数据：</strong> Python脚本处理后的截面数据文件</li>
    </ul>
    
    <h4>📋 使用说明：</h4>
    <ol>
      <li>下载附件中的ZIP文件</li>
      <li>解压后可查看原始数据和合并数据</li>
      <li>原始数据按地区分文件夹存放</li>
      <li>合并数据在"合并-截面数据"文件夹中</li>
    </ol>
    
    <p>如有任何问题，请联系技术支持。</p>
    
    <p>祝您工作顺利！</p>
    ''';
  }

  /// 测试邮件配置
  static Future<bool> testEmailConfiguration(String testRecipient) async {
    try {
      final smtpServer = SmtpServer(
        _smtpHost,
        port: _smtpPort,
        ssl: _useSSL,
        allowInsecure: true,
        username: _senderEmail,
        password: _senderPassword,
        ignoreBadCertificate: true,
      );

      final message = Message()
        ..from = Address(_senderEmail, '百度指数数据系统')
        ..recipients.add(testRecipient)
        ..subject = '邮件配置测试'
        ..text = '这是一封测试邮件，用于验证邮件配置是否正确。';

      final sendReport = await send(message, smtpServer);
      return true; // 如果没有异常，说明发送成功
    } catch (e) {
      print('邮件配置测试失败: $e');
      return false;
    }
  }
}
