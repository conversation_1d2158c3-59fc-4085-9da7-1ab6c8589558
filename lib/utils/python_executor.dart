import 'dart:io';
import 'dart:convert';

/// Python脚本执行工具类
/// 用于在Dart中执行Python脚本
class PythonExecutor {
  PythonExecutor._();

  /// 执行Python数据合并脚本
  /// 
  /// [timestampFolderPath] 时间戳文件夹路径
  /// [onOutput] 输出回调函数，用于实时显示脚本输出
  /// [onProgress] 进度回调函数
  /// 
  /// 返回执行是否成功
  static Future<bool> executeMergeCsvScript({
    required String timestampFolderPath,
    Function(String output)? onOutput,
    Function(String progress)? onProgress,
  }) async {
    try {
      onProgress?.call('正在准备Python脚本...');

      // 获取脚本路径
      final scriptPath = await _getScriptPath();
      if (scriptPath == null) {
        onOutput?.call('❌ 错误：找不到Python合并脚本');
        return false;
      }

      // 检查Python是否可用
      final pythonCommand = await _findPythonCommand();
      if (pythonCommand == null) {
        onOutput?.call('❌ 错误：系统中未找到Python环境');
        return false;
      }

      onProgress?.call('正在执行Python脚本...');
      onOutput?.call('🐍 使用Python命令: $pythonCommand');
      onOutput?.call('📄 脚本路径: $scriptPath');
      onOutput?.call('📁 数据路径: $timestampFolderPath');

      // 执行Python脚本 - 设置环境变量
      final process = await Process.start(
        pythonCommand,
        [scriptPath, timestampFolderPath],
        workingDirectory: Directory.current.path,
        environment: {
          'PYTHONIOENCODING': 'utf-8',
          'PYTHONLEGACYWINDOWSSTDIO': '1',
        },
      );

      // 监听标准输出 - 使用系统默认编码处理
      process.stdout
          .transform(SystemEncoding().decoder)
          .transform(LineSplitter())
          .listen((line) {
        onOutput?.call(line);

        // 解析进度信息
        if (line.contains('处理进度') || line.contains('智能合并进度') ||
            line.contains('progress') || line.contains('Processing')) {
          onProgress?.call('正在处理数据...');
        } else if (line.contains('处理完成') || line.contains('completed')) {
          onProgress?.call('数据处理完成');
        }
      }, onError: (error) {
        onOutput?.call('输出流错误: $error');
      });

      // 监听标准错误 - 使用系统默认编码处理
      process.stderr
          .transform(SystemEncoding().decoder)
          .transform(LineSplitter())
          .listen((line) {
        onOutput?.call('⚠️ $line');
      }, onError: (error) {
        onOutput?.call('错误流错误: $error');
      });

      // 等待进程完成
      final exitCode = await process.exitCode;

      if (exitCode == 0) {
        onOutput?.call('✅ Python脚本执行成功');
        onProgress?.call('数据合并完成');
        return true;
      } else {
        onOutput?.call('❌ Python脚本执行失败，退出码: $exitCode');
        onProgress?.call('数据合并失败');
        return false;
      }

    } catch (e) {
      onOutput?.call('❌ Python脚本执行异常: $e');
      onProgress?.call('执行失败');
      return false;
    }
  }

  /// 检查Python环境和依赖
  static Future<Map<String, dynamic>> checkPythonEnvironment() async {
    final result = <String, dynamic>{
      'pythonAvailable': false,
      'pythonVersion': '',
      'dependenciesInstalled': false,
      'missingDependencies': <String>[],
    };

    try {
      // 查找Python命令
      final pythonCommand = await _findPythonCommand();
      if (pythonCommand == null) {
        result['error'] = 'Python未安装或不在PATH中';
        return result;
      }

      result['pythonAvailable'] = true;

      // 获取Python版本
      final versionProcess = await Process.run(pythonCommand, ['--version']);
      if (versionProcess.exitCode == 0) {
        result['pythonVersion'] = versionProcess.stdout.toString().trim();
      }

      // 检查必需的依赖包
      final requiredPackages = ['pandas', 'tqdm'];
      final missingPackages = <String>[];

      for (final package in requiredPackages) {
        final checkProcess = await Process.run(
          pythonCommand,
          ['-c', 'import $package'],
        );
        
        if (checkProcess.exitCode != 0) {
          missingPackages.add(package);
        }
      }

      result['dependenciesInstalled'] = missingPackages.isEmpty;
      result['missingDependencies'] = missingPackages;

      return result;
    } catch (e) {
      result['error'] = 'Python环境检查失败: $e';
      return result;
    }
  }

  /// 安装Python依赖包
  static Future<bool> installPythonDependencies({
    Function(String output)? onOutput,
  }) async {
    try {
      final pythonCommand = await _findPythonCommand();
      if (pythonCommand == null) {
        onOutput?.call('❌ Python未安装');
        return false;
      }

      onOutput?.call('🔧 正在安装Python依赖包...');

      // 安装pandas和tqdm
      final packages = ['pandas', 'tqdm'];
      
      for (final package in packages) {
        onOutput?.call('📦 安装 $package...');
        
        final process = await Process.start(
          pythonCommand,
          ['-m', 'pip', 'install', package],
        );

        process.stdout
            .transform(utf8.decoder)
            .transform(LineSplitter())
            .listen((line) => onOutput?.call(line));

        process.stderr
            .transform(utf8.decoder)
            .transform(LineSplitter())
            .listen((line) => onOutput?.call('⚠️ $line'));

        final exitCode = await process.exitCode;
        
        if (exitCode != 0) {
          onOutput?.call('❌ $package 安装失败');
          return false;
        }
        
        onOutput?.call('✅ $package 安装成功');
      }

      onOutput?.call('🎉 所有依赖包安装完成');
      return true;
    } catch (e) {
      onOutput?.call('❌ 依赖包安装失败: $e');
      return false;
    }
  }

  /// 查找可用的Python命令
  static Future<String?> _findPythonCommand() async {
    final commands = ['python', 'python3', 'py'];
    
    for (final command in commands) {
      try {
        final result = await Process.run(command, ['--version']);
        if (result.exitCode == 0) {
          return command;
        }
      } catch (e) {
        // 继续尝试下一个命令
      }
    }
    
    return null;
  }

  /// 获取Python脚本路径
  static Future<String?> _getScriptPath() async {
    final scriptPath = '${Directory.current.path}/scripts/merge_csv_data.py';
    final scriptFile = File(scriptPath);
    
    if (await scriptFile.exists()) {
      return scriptPath;
    }
    
    return null;
  }

  /// 验证脚本文件完整性
  static Future<bool> validateScript() async {
    try {
      final scriptPath = await _getScriptPath();
      if (scriptPath == null) return false;

      final scriptFile = File(scriptPath);
      final content = await scriptFile.readAsString();
      
      // 检查脚本是否包含必要的函数
      final requiredFunctions = [
        'safe_merge_csv',
        'process_single_file',
        'merge_chunks_batch',
      ];
      
      for (final function in requiredFunctions) {
        if (!content.contains('def $function')) {
          print('脚本缺少必要函数: $function');
          return false;
        }
      }
      
      return true;
    } catch (e) {
      print('脚本验证失败: $e');
      return false;
    }
  }
}
