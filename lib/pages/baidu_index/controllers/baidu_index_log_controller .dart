import 'dart:async';
import 'dart:convert';
import 'package:bd/pages/baidu_index/baidu_index_logic.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:get/get.dart';
import 'package:puppeteer/protocol/network.dart';
import 'package:puppeteer/puppeteer.dart';
import '../../../model/baidu_user_model.dart';


class LogController extends GetxController {

  BaiduIndexLogic get baiduIndexLogic => Get.find<BaiduIndexLogic>();

  // 批量日志缓存
  List<String> _logBuffer = [];
  Timer? _flushTimer;
  bool _isScrolling = false;

  /// 添加日志 - 批量处理版本
  void addLog(String message) {
    // 添加到缓存
    _logBuffer.add("[${DateTime.now().toString().split('.')[0]}] $message");

    // 批量处理：每50条或每2秒刷新一次
    if (_logBuffer.length >= 50) {
      _flushLogs();
    } else {
      _flushTimer?.cancel();
      _flushTimer = Timer(Duration(seconds: 2), _flushLogs);
    }
  }

  /// 立即添加日志（用于重要消息）
  void addLogImmediate(String message) {
    // 先刷新缓存
    _flushLogs();

    // 立即添加重要日志
    baiduIndexLogic.logs.insert(0, "[${DateTime.now().toString().split('.')[0]}] $message");

    // 限制日志数量
    if (baiduIndexLogic.logs.length > 500) {
      baiduIndexLogic.logs.removeRange(500, baiduIndexLogic.logs.length);
    }

    baiduIndexLogic.update(['logs', 'logs_count']);
    _scheduleScroll();
  }

  /// 批量刷新日志到UI
  void _flushLogs() {
    if (_logBuffer.isEmpty) return;

    // 批量添加到主日志列表
    baiduIndexLogic.logs.insertAll(0, _logBuffer);
    _logBuffer.clear();

    // 限制日志数量
    if (baiduIndexLogic.logs.length > 500) {
      baiduIndexLogic.logs.removeRange(500, baiduIndexLogic.logs.length);
    }

    // 只触发一次UI更新
    baiduIndexLogic.update(['logs', 'logs_count']);
    _scheduleScroll();
  }

  /// 调度滚动操作，避免频繁滚动
  void _scheduleScroll() {
    if (_isScrolling) return;

    _isScrolling = true;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (baiduIndexLogic.logScrollController.hasClients) {
        // 使用jumpTo替代动画，减少性能消耗
        baiduIndexLogic.logScrollController.jumpTo(0);
      }
      _isScrolling = false;
    });
  }

  /// 清空日志
  void clearLogs() {
    // 清空缓存
    _logBuffer.clear();
    _flushTimer?.cancel();

    // 清空主日志
    baiduIndexLogic.logs.clear();
    baiduIndexLogic.update(['logs','logs_count']);
  }

  /// 强制刷新所有缓存的日志
  void forceFlush() {
    _flushLogs();
  }

  @override
  void onClose() {
    _flushTimer?.cancel();
    super.onClose();
  }
}