import 'dart:async';
import 'dart:convert';
import 'package:bd/pages/baidu_index/baidu_index_logic.dart';
import 'package:http/http.dart' as http;
import 'package:get/get.dart';
import 'package:puppeteer/protocol/network.dart';
import 'package:puppeteer/puppeteer.dart';
import '../../../model/baidu_user_model.dart';
import 'baidu_index_log_controller .dart';


class UserController extends GetxController {

  List<BaiDuUsers> users = [];
  String userApiKey ='';
  String userApiKeyTime ='';

  LogController get logController => Get.find<LogController>();
  BaiduIndexLogic get baiduIndexLogic => Get.find<BaiduIndexLogic>();


  loginAccount(BaiDuUsers user) async {
    // 创建参数列表
    List<String> args = [];

    // 如果配置了代理，添加代理设置
    if (user.isProxy && user.proxyAddress != null && user.proxyPort != null) {
      args.add('--proxy-server=${user.proxyAddress}:${user.proxyPort}');
    }

    // 启动览器，直接使用 args
    var browser = await puppeteer.launch(
      headless: false,
      args: args,  // 直接传入参数列表
    );

    var page = await browser.newPage();

    // 如果配置了代理认证，设置认证信息
    if (user.isProxy &&
        user.proxyUsername != null &&
        user.proxyUsername!.isNotEmpty &&
        user.proxyPassword != null) {
      await page.authenticate(
          username: user.proxyUsername!,
          password: user.proxyPassword!
      );
    }

    await page.setRequestInterception(true);
    // 创建 Completer 用于等待流任务完成
    Completer<void> onResuestCompleter = Completer<void>();

    // 原有的请求拦截逻辑
    page.onRequest.listen((request) async {
      if (request.url.contains('https://dlswbr.baidu.com/heicha/mm/2057/acs-2057.js')) {

        var response = await http.get(Uri.parse(request.url));
        if (response.statusCode == 200) {
          var originalJsContent = response.body;

          // String originalJsContent1 = "a8(b('0x79') + ae + '\\x5f' + eg(a2, a0, a1));";
          // String originalJsContent2 = "a8('\\x31\\x37\\x32\\x37\\x31\\x37\\x35\\x38\\x36\\x34\\x30\\x34\\x32\\x5f' + ae + '\\x5f' + eg(a2, a0, a1));";

          // 定义正则表达式
          RegExp regex1 = RegExp(r"a8\(b\('([^']+)'\)\+ae\+");
          RegExp regex2 = RegExp(r"a8\('([^']+)'\s*\+\s*ae");

          String timeKet = "";

          String modifiedJsContent = "";

          // 封装匹配逻辑
          String matchDynamicValue(String content, RegExp regex) {
            Match? match = regex.firstMatch(content);
            if (match != null) {
              return match.group(1)!; // 提取动态值
            }
            return ""; // 未找到匹配的值
          }

          // 先匹配第一个字符串
          timeKet = matchDynamicValue(originalJsContent, regex1);
          // 如果第一个字符串没有匹配到，则匹配第二个字符串
          if (timeKet.isEmpty) {
            timeKet = matchDynamicValue(originalJsContent, regex2);
            logController.addLog("regex2----$timeKet");
            modifiedJsContent = originalJsContent.replaceFirst(
              "function ek(){",  // 查找目标函数 eg
              '''
            window['wrrtime']= '$timeKet';
            window['wrr'] = a0;
            function ek(){
          ''',  // 在函数头部插入日志
            );

          }else{
            timeKet = "b('$timeKet')";
            modifiedJsContent = originalJsContent.replaceFirst(
              "function ek(){",  // 查找目标函数 eg
              '''
            window['wrrtime']= $timeKet;
            window['wrr'] = a0;
            function ek(){
          ''',  // 在函数头部插入日志
            );

          }
          // 输出结果
          if (timeKet.isNotEmpty) {
            logController.addLog('匹配到的动态值: $timeKet');
          } else {
            logController.addLog('未找到匹配的值');

          }


          await request.respond(
            status: 200,
            contentType: 'application/javascript',
            body: modifiedJsContent,
          );
        } else {
          // 如果获取原始文件失败，继续请求而不1
          await request.continueRequest();
        }
      }
      else {
        // 对其他请求，继续请求
        await request.continueRequest();
      }
    });

    // 原有的响应处理逻辑
    page.onResponse.listen((response) async {
      if (response.url.contains("https://index.baidu.com/api/SearchApi/index")) {
        if (response.status == 200) {
          // 获取特定 URL 的 cookies
          List<Cookie> cookies = await page.cookies(urls: ['https://index.baidu.com/api/SearchApi/index']);
          logController.addLog('Cookies for https://index.baidu.com: $cookies');
          for (var cookie in cookies) {
            if (cookie.name == 'BDUSS') {
              logController.addLog('Cookie: Name = ${cookie.name}, Value = ${cookie.value}, Domain = ${cookie.domain}, Path = ${cookie.path}');
              user.cookie = "${cookie.name}=${cookie.value};";
            }

            if (cookie.name == 'ab_sr') {
              user.cookie +="${cookie.name}=${cookie.value};";
            }
            logController.addLog(user.cookie);
          }
          // 等待元素加载
          await page.waitForSelector('.username-text');
          var username = await page.evaluate('''() => {
           const element = document.querySelector('.username-text');
           return element ? element.innerText : null; // 获取元素文本或返回 null
           }''');


          // 打印获取到的用户名
          logController.addLog('Username: $username');
          // 判断是否被封号（根据实际响应内容判断）
          await Future.delayed(Duration(milliseconds: 200));
          final body = await response.text;
          logController.addLog(body);
          final jsonBody = jsonDecode(body) as Map<String, dynamic>;

          // 正确字段提取
          final status = jsonBody['status'] ?? -1;
          final data = jsonBody['data']?.toString() ?? ''; // 确保为字符串
          final message = jsonBody['message']?.toString() ?? '无错误信息';

          logController.addLog('状态码: $status');
          logController.addLog('数据: $data');
          logController.addLog('消息: $message');
          if (data == "" && message == "request block"){
            user.isStart = false;
            user.username = username;
            user.isError = true;

          }else{
            user.isStart = true;
            user.username = username;
            user.isError = false;
          }

          baiduIndexLogic.update(['list', "two"]);

          // 在此关闭浏览器
          await browser.close();
        } else {
          logController.addLog("当前账户不可用");
        }

        // 完成后标记 Completer 完成
        if (!onResuestCompleter.isCompleted) onResuestCompleter.complete();
      }
    });

    // 访问目标网页
    await page.goto('https://index.baidu.com/v2/main/index.html#/trend/%E5%8D%8E%E4%B8%BA?words=%E5%8D%8E%E4%B8%BA');

    // 获取 wrr 和 wrrtime
    var wrr = await page.evaluate('''
      function() {
        console.log("123123123213");
        return window['wrr'];
      }
    ''');

    var wrrtime = await page.evaluate('''
      function() {
        return window['wrrtime'];
      }
    ''');


    if( wrr != ""){
      userApiKey = wrr;
      user.apiKey = wrr;

    }else{
      user.apiKey = userApiKey;
    }
    if( wrrtime != ""){
      userApiKeyTime = wrrtime;
      user.apiKeyTime = wrrtime.replaceAll('_', '');

    }else{
      user.apiKeyTime = userApiKeyTime.replaceAll('_', '');
    }

    logController.addLog(user.apiKey);
    logController.addLog(user.apiKeyTime);

    // 阻塞等待 onResuest 事件完成
    await onResuestCompleter.future;
  }


  bool isUserAvailable(BaiDuUsers user) {
    // 检查用户基础条件：
    // 1. isStart 必须为 true
    // 2. cookie 不能为空
    // 3. apiKey 不能为空
    return user.isStart &&
        user.cookie.isNotEmpty &&
        user.apiKey.isNotEmpty;
  }

  //   // 获取选中用户
  List<BaiDuUsers> getSelectedUsers() {
    return users.where((user) => user.isSelected).toList();
  }

  bool get isAllSelected => users.isNotEmpty && users.every((user) => user.isSelected);


  void toggleAllSelection(bool value) {
    for (var user in users) {
      user.isSelected = value;
    }
    baiduIndexLogic.update(['list']);
  }

  void toggleUserSelection(int index, bool value) {
    users[index].isSelected = value;
    baiduIndexLogic.update(['list']);
  }


// 获取可用用户数量
  int getAvailableUserCount() {
    return users.where((user) => isUserAvailable(user)).length;
  }

  // 检查是否有可用账号
  bool hasAvailableAccount() {
    return users.any((user) =>
    user.isStart &&
        user.username != "暂未登录" &&
        user.apiKey != null &&
        user.apiKey!.isNotEmpty
    );
  }

}