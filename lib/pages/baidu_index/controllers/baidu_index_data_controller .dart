import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:bd/model/baidu_model.dart';
import 'package:bd/model/data_model.dart';
import 'package:bd/pages/baidu_index/baidu_index_logic.dart';
import 'package:bd/pages/baidu_index/controllers/baidu_index_log_controller%20.dart';
import 'package:bd/pages/baidu_index/controllers/baidu_index_task_controller%20.dart';
import 'package:bd/pages/baidu_index/controllers/baidu_index_user_controller.dart';
import 'package:bd/utils/date.dart';
import 'package:bd/utils/notification_util.dart';
import 'package:bd/utils/store_util.dart';
import 'package:bd/utils/compression_util.dart';
import 'package:bd/utils/email_util.dart';
import 'package:bd/utils/python_executor.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:csv/csv.dart';
import 'package:path/path.dart' as path;

//# 数据处理控制器
class DataController extends GetxController {

  UserController get userController => Get.find<UserController>();
  LogController get logController => Get.find<LogController>();
  BaiduIndexLogic get baiduIndexLogic => Get.find<BaiduIndexLogic>();
  TaskController get taskController => Get.find<TaskController>();


//  数据处理
  Future<void> handlingData2() async {
    String jsonString = json.encode(baiduIndexLogic.data.map((e) => e.toJson()).toList());
    // logController.addLog(jsonString);

    List<TreeNode> treeNodes = taskController.flattenCheckedNodes(baiduIndexLogic.area_data);

    for (var keyword in baiduIndexLogic.keyWords) {
      logController.addLog(keyword);

      for (TreeNode treeNode in treeNodes) {
        logController.addLog(treeNode.name);
        if(treeNode.name.isEmpty){
          logController.addLog("× 错误-城市为空");

          return;
        }


        // 创建各个时间段的数据集合
        List<List<dynamic>> dailyData = [];
        List<List<dynamic>> monthlyData = [];
        List<List<dynamic>> weeklyData = [];
        List<List<dynamic>> yearlyData = [];

        var matchingDataModels = baiduIndexLogic.data.where((dataModel) => dataModel.region == treeNode.name);

        logController.addLog("✨ 开始保存数据-地区${treeNode.name}-关键词$keyword");

        // 处理每个数据项
        var futures = <Future>[];

        for (var dataModel in matchingDataModels) {
          futures.add(Future(() async {
            await _processDataModel(dataModel, keyword, treeNode, dailyData, monthlyData, weeklyData, yearlyData);
          }));
        }

        // 等待所有异步操作完成
        await Future.wait(futures);

        // 添加标题行并处理列的删除
        _addHeadersAndRemoveColumns(dailyData, "日");
        _addHeadersAndRemoveColumns(monthlyData, "月");
        _addHeadersAndRemoveColumns(weeklyData, "周");
        _addHeadersAndRemoveColumns(yearlyData, "年");

        // 生成并保存 CSV 文件
        String path = await StoreUtil.checkAndCreateFolders(treeNode.name, keyword);

        if(baiduIndexLogic.r){
          // 按日保存
          String dailyFileName = "${treeNode.name}-日-$keyword-${baiduIndexLogic.startDate}-${baiduIndexLogic.endDate}";
          exportCsv(dailyData, path, dailyFileName);
          logController.addLog("✅ 保存数据成功-地区${treeNode.name}-关键词$keyword - 按日");
        }

        if(baiduIndexLogic.y){
          // 按月保存
          String monthlyFileName = "${treeNode.name}-月-$keyword-${baiduIndexLogic.startDate}-${baiduIndexLogic.endDate}";
          exportCsv(monthlyData, path, monthlyFileName);
          logController.addLog("✅ 保存数据成功-地区${treeNode.name}-关键词$keyword - 按月");
        }

        if(baiduIndexLogic.z){
          // 按周保存
          String weeklyFileName = "${treeNode.name}-周-$keyword-${baiduIndexLogic.startDate}-${baiduIndexLogic.endDate}";
          exportCsv(weeklyData, path, weeklyFileName);
          logController.addLog("✅ 保存数据成功-地区${treeNode.name}-关键词$keyword - 按周");
        }

        if(baiduIndexLogic.n){
          // 按年保存
          String yearlyFileName = "${treeNode.name}-年-$keyword-${baiduIndexLogic.startDate}-${baiduIndexLogic.endDate}";
          exportCsv(yearlyData, path, yearlyFileName);
          logController.addLog("✅ 保存数据成功-地区${treeNode.name}-关键词$keyword - 按年");
        }


      }
    }
    NotificationUtil.show(
      title: '数据保存成功',
      body: '已保存所有关键词的数据，请在运行目录查看！',
    );
  }

  Future<void> handlingData0() async {
    // 1. 预先获取所需数据，避免重复调用
    final treeNode = taskController.flattenCheckedNodes(baiduIndexLogic.area_data);
    final yearDetails = daysInEachYear(baiduIndexLogic.startDate, baiduIndexLogic.endDate);

    // 2. 使用并发处理生成每日数据
    final _data = await genreteDailyData();

    // 3. 优化数据查找 - 使用Map存储，提高查找效率
    final dataMap = {
      for (var item in _data)
        "${item[0]}_${item[1]}_${item[2]}": item  // key: keyword_region_date
    };

    // 4. 并发处理每年的数据
    await Future.wait<void>(
        yearDetails.map((yearDetail) {
          return Future<void>.sync(() async {
            // 5. 优化内存分配 - 预分配数组大小
            final dailyData = [["日期", "地区"]];
            for (var keyword in baiduIndexLogic.keyWords) {
              dailyData[0].addAll(["${keyword}_全部", "${keyword}_PC", "${keyword}_Wise"]);
            }

            // 6. 优化日期处理
            final dates = _generateDateRange(yearDetail.start, yearDetail.end);

            // 7. 预分配行数据
            final rowCount = dates.length * treeNode.length;
            final initialRows = List.generate(
                rowCount,
                    (index) {
                  final dateIndex = index ~/ treeNode.length;
                  final nodeIndex = index % treeNode.length;
                  return [dates[dateIndex], treeNode[nodeIndex].name];
                }
            );
            dailyData.addAll(initialRows);

            // 8. 批量处理数据
            var processedCount = 0;
            final totalCount = dates.length * treeNode.length * baiduIndexLogic.keyWords.length;

            // 9. 使用批量更新减少UI更新频率
            for (var date in dates) {
              for (var node in treeNode) {
                for (var keyword in baiduIndexLogic.keyWords) {
                  final key = "${keyword}_${node.name}_$date";
                  final result = dataMap[key];

                  if (result != null) {
                    final row = dailyData.firstWhere(
                          (row) => row[0] == date && row[1] == node.name,
                      orElse: () => [],
                    );

                    if (row.isNotEmpty) {
                      row.addAll([
                        result[3]?.toString() ?? '0',
                        result[4]?.toString() ?? '0',
                        result[5]?.toString() ?? '0'
                      ]);
                    }
                  }

                  processedCount++;
                  if (processedCount % 100 == 0) { // 每处理100条数据更新一次进度
                    logController.addLog("⏳ 处理进度: ${(processedCount / totalCount * 100).toStringAsFixed(1)}%");
                  }
                }
              }
            }

            // 10. 移除空行
            dailyData.removeWhere((row) =>
            row.length <= 2 || row.skip(2).every((value) => value == null)
            );

            // 11. 保存数据
            final year = yearDetail.start.substring(0, 4);
            final resultFolderPath = await StoreUtil.checkAndCreateFolders('', '数据提取结果');
            exportCsv(dailyData, resultFolderPath, year);
            logController.addLog("✅ 保存数据成功 - $year年度数据");
          });
        }).toList()
    );

    NotificationUtil.show(
      title: '数据保存成功',
      body: '已保存所有关键词的数据，请在运行目录查看！',
    );
  }

  // 辅助函数：生成日期范围
  List<String> _generateDateRange(String start, String end) {
    final startDate = DateTime.parse(start);
    final endDate = DateTime.parse(end);
    return List.generate(
        endDate.difference(startDate).inDays + 1,
            (index) => DateFormat('yyyy-MM-dd').format(startDate.add(Duration(days: index)))
    );
  }

  List<dynamic>? findRecord(List<List<dynamic>> data, List<dynamic> target) {
    return data.firstWhere(
          (row) => row[0] == target[0] && row[1] == target[1] && row[2] == target[2],
      orElse: () => [], // 如果没有找到匹配项，返回 null
    );
  }


  // 处理数据模型的方法
  Future<void> _processDataModel(DataModel dataModel, String keyword, TreeNode treeNode,
      List<List<dynamic>> dailyData, List<List<dynamic>> monthlyData,
      List<List<dynamic>> weeklyData, List<List<dynamic>> yearlyData) async {
    for (var entry in dataModel.data!.entries) {
      // 解构 entry，直接使用 entry.key 和 entry.value
      var year = entry.key;  // 获取年份
      var dramas = entry?.value;  // 获取对应年份的所有 dramas

      for (var drama in dramas!) {
        if (drama.keyword == keyword) {
          // logController.addLog('Found match: Region = ${dataModel.region}, Year = $year, Drama = ${drama.toJson()}');

          DateTime startDate = DateTime.parse(drama.startData ?? '');
          DateTime endDate = DateTime.parse(drama.endData ?? '');

          // 生成日期范围
          List<DateTime> timestampList = List<DateTime>.generate(
            endDate.difference(startDate).inDays + 1,
                (index) => startDate.add(Duration(days: index)),
          );
          List<String> dateList = timestampList
              .map((timestamp) => DateFormat('yyyy-MM-dd').format(timestamp))
              .toList();



          if(baiduIndexLogic.r){
            // 按日处理
            dailyData.addAll(_processDailyData(drama, dataModel.region!, dateList));
          }
          if (baiduIndexLogic.y){
            // 按月加总
            monthlyData.addAll(await _processMonthlyData(drama, dataModel.region!, dateList, timestampList));
          }
          if (baiduIndexLogic.z){
            // 按周加总
            weeklyData.addAll(await _processWeeklyData(drama, dataModel.region!, dateList, timestampList));
          }

          if (baiduIndexLogic.n){
            // 按年加总
            yearlyData.addAll(await _processYearlyData(drama, dataModel.region!, dateList, timestampList));
          }


        }
      }
    }
  }


  List<List<dynamic>> _processDailyData(
      Drama drama, String region, List<String> dateList) {
    logController.addLog("drama--${drama.pc?.length}");
    logController.addLog("drama--${drama.pc}");
    logController.addLog("dateList--${dateList.length}");

    List<List<dynamic>> dailyData = [];
    for (int i = 0; i < dateList.length; i++) {
      dailyData.add([
        drama.keyword,
        region,
        dateList[i],
        drama.all?[i],
        drama.pc?[i],
        drama.wise?[i],
      ]);
    }
    return dailyData;
  }


  // 添加表头并删除不需要的列
  void _addHeadersAndRemoveColumns(List<List<dynamic>> data, String type) {
    if (data.isNotEmpty && data[0][0] != "关键词") {
      switch (type) {
        case "日":
          data.insert(0, ["关键词", "地区", "日期", "全部", "PC端", "移动端"]);
          break;
        case "月":
          data.insert(0, ["关键词", "地区", "月份", "全部", "PC端", "移动端"]);
          break;
        case "周":
          data.insert(0, ["关键词", "地区", "周次", "全部", "PC端", "移动端"]);
          break;
        case "年":
          data.insert(0, ["关键词", "地区", "年份", "全部", "PC端", "移动端"]);
          break;
      }

      // 调用方法删除相应的列
      removeColumnsIfNeeded(data, baiduIndexLogic.AnyIndexActives);
    }
  }

  void removeColumnsIfNeeded(List<List<dynamic>> data, List<dynamic> columnsToRemove) {
    // 调用函数生成最终的列表
    List<String> activeIndexes = baiduIndexLogic.generateIndexList(columnsToRemove);

    // 遍历列名列表
    for (var columnName in activeIndexes) {

      // 如果列名存在于表头
      if (data[0].contains(columnName.toString())) {
        // 调用删除列的方法
        removeColumn(data, columnName.toString());
      }
    }
  }

  void removeColumn(List<List<dynamic>> data, String columnName) {
    // 找到目标列的索引
    int columnIndex = data[0].indexOf(columnName);

    if (columnIndex != -1) {
      // 删除每行对应索引的列
      for (var row in data) {
        row.removeAt(columnIndex);
      }
    }
  }


  void exportCsv(List<List<dynamic>> csvData, String path, String fileName) async {
    String csv = const ListToCsvConverter().convert(csvData);

    // 添加 UTF-8 BOM
    List<int> bom = [0xEF, 0xBB, 0xBF];
    List<int> csvBytes = utf8.encode(csv);
    List<int> bomCsvBytes = bom + csvBytes;

    // 组合路径和文件名
    final File file = File('$path/$fileName.csv');

    // 将CSV数据写入文件
    await file.writeAsBytes(bomCsvBytes);
  }

  // 新增：支持时间戳文件夹结构的自动保存方法
  Future<void> handlingDataWithTimestamp(String timestamp) async {
    List<TreeNode> treeNodes = taskController.flattenCheckedNodes(baiduIndexLogic.area_data);

    logController.addLog("🚀 开始自动保存数据到时间戳文件夹: $timestamp");

    for (var keyword in baiduIndexLogic.keyWords) {
      logController.addLog("📝 处理关键词: $keyword");

      for (TreeNode treeNode in treeNodes) {
        logController.addLog("📍 处理地区: ${treeNode.name}");
        if(treeNode.name.isEmpty){
          logController.addLog("× 错误-城市为空");
          return;
        }

        // 创建各个时间段的数据集合
        List<List<dynamic>> dailyData = [];
        List<List<dynamic>> monthlyData = [];
        List<List<dynamic>> weeklyData = [];
        List<List<dynamic>> yearlyData = [];

        var matchingDataModels = baiduIndexLogic.data.where((dataModel) => dataModel.region == treeNode.name);

        logController.addLog("✨ 开始保存数据-地区${treeNode.name}-关键词$keyword");

        // 处理每个数据项
        var futures = <Future>[];

        for (var dataModel in matchingDataModels) {
          futures.add(Future(() async {
            await _processDataModel(dataModel, keyword, treeNode, dailyData, monthlyData, weeklyData, yearlyData);
          }));
        }

        // 等待所有异步操作完成
        await Future.wait(futures);

        // 添加标题行并处理列的删除
        _addHeadersAndRemoveColumns(dailyData, "日");
        _addHeadersAndRemoveColumns(monthlyData, "月");
        _addHeadersAndRemoveColumns(weeklyData, "周");
        _addHeadersAndRemoveColumns(yearlyData, "年");

        // 使用新的时间戳文件夹结构保存文件
        String path = await StoreUtil.checkAndCreateTimestampFolders(timestamp, treeNode.name);

        if(baiduIndexLogic.r){
          // 按日保存
          String dailyFileName = "${treeNode.name}-日-$keyword-${baiduIndexLogic.startDate}-${baiduIndexLogic.endDate}";
          exportCsv(dailyData, path, dailyFileName);
          logController.addLog("✅ 保存数据成功-地区${treeNode.name}-关键词$keyword - 按日");
        }

        if(baiduIndexLogic.y){
          // 按月保存
          String monthlyFileName = "${treeNode.name}-月-$keyword-${baiduIndexLogic.startDate}-${baiduIndexLogic.endDate}";
          exportCsv(monthlyData, path, monthlyFileName);
          logController.addLog("✅ 保存数据成功-地区${treeNode.name}-关键词$keyword - 按月");
        }

        if(baiduIndexLogic.z){
          // 按周保存
          String weeklyFileName = "${treeNode.name}-周-$keyword-${baiduIndexLogic.startDate}-${baiduIndexLogic.endDate}";
          exportCsv(weeklyData, path, weeklyFileName);
          logController.addLog("✅ 保存数据成功-地区${treeNode.name}-关键词$keyword - 按周");
        }

        if(baiduIndexLogic.n){
          // 按年保存
          String yearlyFileName = "${treeNode.name}-年-$keyword-${baiduIndexLogic.startDate}-${baiduIndexLogic.endDate}";
          exportCsv(yearlyData, path, yearlyFileName);
          logController.addLog("✅ 保存数据成功-地区${treeNode.name}-关键词$keyword - 按年");
        }
      }
    }

    logController.addLog("🎉 自动保存完成，时间戳文件夹: $timestamp");
    NotificationUtil.show(
      title: '自动保存成功',
      body: '已保存所有关键词的数据到时间戳文件夹，准备进行后续处理！',
    );
  }

  // 完整的自动保存流程：保存→合并→压缩→发送邮件
  Future<void> executeAutoSaveWorkflow() async {
    if (!baiduIndexLogic.enableAutoSave) {
      logController.addLog("⚠️ 自动保存未启用，跳过自动保存流程");
      return;
    }

    if (baiduIndexLogic.recipientEmail.isEmpty) {
      logController.addLog("❌ 收件人邮箱未设置，无法执行自动保存流程");
      NotificationUtil.show(
        title: '自动保存失败',
        body: '请先设置收件人邮箱地址！',
      );
      return;
    }

    try {
      logController.addLog("🚀 开始执行自动保存完整流程...");

      // 第1步：生成时间戳并保存数据
      final timestamp = baiduIndexLogic.generateTimestamp();
      baiduIndexLogic.currentSaveTimestamp = timestamp;

      logController.addLog("📅 生成时间戳: $timestamp");
      await handlingDataWithTimestamp(timestamp);

      // 第2步：执行Python脚本合并数据
      logController.addLog("🐍 开始执行Python数据合并脚本...");
      final timestampFolderPath = await StoreUtil.getTimestampRootPath(timestamp);

      bool pythonSuccess = false;
      try {
        pythonSuccess = await PythonExecutor.executeMergeCsvScript(
          timestampFolderPath: timestampFolderPath,
          onOutput: (output) {
            // 过滤掉可能导致编码错误的输出
            if (output.isNotEmpty && !output.contains('FormatException')) {
              logController.addLog("Python: $output");
            }
          },
          onProgress: (progress) => logController.addLog("📊 $progress"),
        );
      } catch (e) {
        logController.addLog("❌ Python脚本执行异常: $e");
        pythonSuccess = false;
      }

      if (!pythonSuccess) {
        logController.addLog("❌ Python脚本执行失败，终止自动保存流程");
        NotificationUtil.show(
          title: '自动保存失败',
          body: 'Python数据合并脚本执行失败，请检查Python环境和依赖包！',
        );
        return;
      }

      // 第3步：压缩文件夹
      logController.addLog("📦 开始压缩数据文件夹...");
      final zipFileName = CompressionUtil.generateZipFileName(timestamp);
      final zipFilePath = path.join(Directory.current.path, zipFileName);

      final compressionSuccess = await CompressionUtil.compressFolderToZip(
        sourceFolderPath: timestampFolderPath,
        outputZipPath: zipFilePath,
        onProgress: (fileName) => logController.addLog("📁 压缩文件: $fileName"),
      );

      if (!compressionSuccess) {
        logController.addLog("❌ 文件压缩失败，终止自动保存流程");
        NotificationUtil.show(
          title: '自动保存失败',
          body: '数据文件压缩失败！',
        );
        return;
      }

      // 第4步：发送邮件
      logController.addLog("📧 开始发送邮件到: ${baiduIndexLogic.recipientEmail}");

      // 统计数据
      final keywordCount = baiduIndexLogic.keyWords.length;
      final regionCount = taskController.flattenCheckedNodes(baiduIndexLogic.area_data).length;

      final emailSuccess = await EmailUtil.sendBaiduIndexDataEmail(
        recipientEmail: baiduIndexLogic.recipientEmail,
        zipFilePath: zipFilePath,
        timestamp: timestamp,
        keywordCount: keywordCount,
        regionCount: regionCount,
        onProgress: (status) => logController.addLog("📧 $status"),
      );

      if (emailSuccess) {
        logController.addLog("🎉 自动保存完整流程执行成功！");
        NotificationUtil.show(
          title: '自动保存完成',
          body: '数据已成功保存、合并、压缩并发送到指定邮箱！',
        );

        // 可选：删除本地ZIP文件以节省空间
        try {
          await File(zipFilePath).delete();
          logController.addLog("🗑️ 已清理本地ZIP文件");
        } catch (e) {
          logController.addLog("⚠️ 清理本地ZIP文件失败: $e");
        }

      } else {
        logController.addLog("❌ 邮件发送失败，但数据已保存和压缩");
        NotificationUtil.show(
          title: '部分完成',
          body: '数据已保存和压缩，但邮件发送失败！',
        );
      }

    } catch (e) {
      logController.addLog("❌ 自动保存流程异常: $e");
      NotificationUtil.show(
        title: '自动保存失败',
        body: '执行过程中发生异常：$e',
      );
    }
  }


  Future<List<List>> genreteDailyData()async{
    List<TreeNode> treeNodes = taskController.flattenCheckedNodes(baiduIndexLogic.area_data);
    // 创建各个时间段的数据集合
    List<List<dynamic>> dailyData = [];

    for (var keyword in baiduIndexLogic.keyWords) {
      logController.addLog(keyword);

      for (TreeNode treeNode in treeNodes) {
        logController.addLog(treeNode.name);
        if(treeNode.name.isEmpty){
          logController.addLog("× 错误-城市为空");

          return [[]];
        }



        List<List<dynamic>> monthlyData = [];
        List<List<dynamic>> weeklyData = [];
        List<List<dynamic>> yearlyData = [];

        var matchingDataModels = baiduIndexLogic.data.where((dataModel) => dataModel.region == treeNode.name);


        // 处理每个数据项
        var futures = <Future>[];

        for (var dataModel in matchingDataModels) {
          futures.add(Future(() async {
            await _processDataModel(dataModel, keyword, treeNode, dailyData, monthlyData, weeklyData, yearlyData);
          }));
        }

        // 等待所有异步操作完成
        await Future.wait(futures);

        // 添加标题行并处理列的删除
        _addHeadersAndRemoveColumns(dailyData, "日");
        _addHeadersAndRemoveColumns(monthlyData, "月");
        _addHeadersAndRemoveColumns(weeklyData, "周");
        _addHeadersAndRemoveColumns(yearlyData, "年");




      }
    }

    return dailyData;

  }


  Future<List<List<dynamic>>> _processMonthlyData(
      Drama drama, String region, List<String> dateList, List<DateTime> timestampList) async {
    Map<String, List<num>> monthlySum = {};
    for (int i = 0; i < dateList.length; i++) {
      String month = DateFormat('yyyy-MM').format(timestampList[i]);
      if (!monthlySum.containsKey(month)) {
        monthlySum[month] = [0, 0, 0];
      }
      monthlySum[month]![0] += drama.all?[i] ?? 0;
      monthlySum[month]![1] += drama.pc?[i] ?? 0;
      monthlySum[month]![2] += drama.wise?[i] ?? 0;
    }

    List<List<dynamic>> monthlyData = [];
    monthlySum.forEach((month, sums) {
      logController.addLog("1sums----,$sums");
      DateTime startMonth = DateTime.parse(month + '-01');
      DateTime endMonth = DateTime(startMonth.year, startMonth.month + 1, 0);
      // endMonth.day

      String monthRange = "${DateFormat('yyyy-MM-dd').format(startMonth)} ~ ${DateFormat('yyyy-MM-dd').format(endMonth)}";
      if(baiduIndexLogic.sjjz){
        monthlyData.add([
          drama.keyword,
          region,
          monthRange,
          sums[0].toInt(),
          sums[1].toInt(),
          sums[2].toInt(),
        ]);
      }
      if(baiduIndexLogic.sjpj){
        monthlyData.add([
          drama.keyword,
          region,
          monthRange,
          (sums[0].toInt()/endMonth.day),
          (sums[1].toInt()/endMonth.day),
          (sums[2].toInt()/endMonth.day),
        ]);
      }
    });

    return monthlyData;
  }


  Future<List<List<dynamic>>> _processWeeklyData(
      Drama drama, String region, List<String> dateList, List<DateTime> timestampList) async {
    Map<String, List<num>> weeklySum = {};
    for (int i = 0; i < dateList.length; i++) {
      DateTime monday = baiduIndexLogic.getStartOfWeek(timestampList[i]);
      String week = DateFormat('yyyy-MM-dd').format(monday);
      if (!weeklySum.containsKey(week)) {
        weeklySum[week] = [0, 0, 0];
      }
      weeklySum[week]![0] += drama.all?[i] ?? 0;
      weeklySum[week]![1] += drama.pc?[i] ?? 0;
      weeklySum[week]![2] += drama.wise?[i] ?? 0;
    }

    List<List<dynamic>> weeklyData = [];
    weeklySum.forEach((week, sums) {
      DateTime monday = DateTime.parse(week);
      DateTime sunday = monday.add(Duration(days: 6));
      // DateTime weekStartDate = getStartOfWeek(timestampList[0], int.parse(week));
      // DateTime weekEndDate = weekStartDate.add(Duration(days: 6));
      // String weekRange = "${DateFormat('yyyy-MM-dd').format(weekStartDate)} ~ ${DateFormat('yyyy-MM-dd').format(weekEndDate)}";
      String weekRange = "${DateFormat('yyyy-MM-dd').format(monday)}~${DateFormat('yyyy-MM-dd').format(sunday)}";
      print("weekRange--$weekRange sums[0]--${sums[0]}  sums[1]--${sums[1]}  sums[2]--${sums[2]}");
      if(baiduIndexLogic.sjjz){
        weeklyData.add([
          drama.keyword,
          region,
          weekRange,
          sums[0].toInt(),
          sums[1].toInt(),
          sums[2].toInt(),
        ]);
      }
      if(baiduIndexLogic.sjpj){
        weeklyData.add([
          drama.keyword,
          region,
          weekRange,
          sums[0].toInt()/7,
          sums[1].toInt()/7,
          sums[2].toInt()/7,
        ]);
      }

    });

    return weeklyData;
  }

  Future<List<List<dynamic>>> _processYearlyData(
      Drama drama, String region, List<String> dateList, List<DateTime> timestampList) async {
    Map<String, List<num>> yearlySum = {};
    for (int i = 0; i < dateList.length; i++) {
      String year = DateFormat('yyyy').format(timestampList[i]);
      if (!yearlySum.containsKey(year)) {
        yearlySum[year] = [0, 0, 0];
      }
      yearlySum[year]![0] += drama.all?[i] ?? 0;
      yearlySum[year]![1] += drama.pc?[i] ?? 0;
      yearlySum[year]![2] += drama.wise?[i] ?? 0;
    }

    List<List<dynamic>> yearlyData = [];
    yearlySum.forEach((year, sums) {

      if (baiduIndexLogic.sjjz){
        yearlyData.add([
          drama.keyword,
          region,
          year,
          sums[0].toInt(),
          sums[1].toInt(),
          sums[2].toInt(),
        ]);
      }



      if (baiduIndexLogic.sjpj){
        int currentYear = getDaysInYear(int.parse(year));
        yearlyData.add([
          drama.keyword,
          region,
          year,
          sums[0].toInt()/currentYear,
          sums[1].toInt()/currentYear,
          sums[2].toInt()/currentYear,
        ]);
      }


    });

    return yearlyData;
  }


  int getDaysInYear(int year) {
    // Leap year check
    if ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)) {
      return 366; // Leap year
    } else {
      return 365; // Regular year
    }
  }




}