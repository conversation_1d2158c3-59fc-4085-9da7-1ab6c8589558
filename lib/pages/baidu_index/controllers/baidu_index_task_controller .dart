import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:bd/model/data_model.dart';
import 'package:bd/model/task_result_model.dart';
import 'package:bd/utils/encrypt.dart';
import 'package:bd/utils/http_client.dart';
import 'package:bd/utils/notification_util.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:get/get.dart';
import 'package:puppeteer/protocol/network.dart';
import 'package:puppeteer/puppeteer.dart';
import '../../../model/baidu_model.dart';
import '../../../model/baidu_user_model.dart';
import '../../../utils/date.dart';
import '../baidu_index_logic.dart';
import 'baidu_index_log_controller .dart';
import 'baidu_index_user_controller.dart';
import 'package:intl/intl.dart';

// 定义任务状态枚举

enum TaskState {
  initial,    // 初始状态
  running,    // 任务进行中
  error,      // 任务出错
  stopped     // 手动停止
}

class TaskController  extends GetxController {

  UserController get userController => Get.find<UserController>();
  LogController get logController => Get.find<LogController>();
  BaiduIndexLogic get baiduIndexLogic => Get.find<BaiduIndexLogic>();

  // 任务收集缓存
  Map<String, Map<String, List<TaskModel>>>? _cachedTasks;
  int _lastDataHash = 0;
  bool _tasksCacheValid = false;

  TaskState taskState = TaskState.initial;

  // 创建实例
  final httpClientUtil = HttpClientUtil();

  String SearchApi = "https://index.baidu.com/api/SearchApi/index";
  String FeedSearchApi = "https://index.baidu.com/api/FeedSearchApi/getFeedIndex";
  String PPCApi = "https://index.baidu.com/insight/brand/queryBrandIndex";


  Future<void> distributeTasksToUsers() async {
    if(baiduIndexLogic.isAnyIndexActive == "brand"){
      await ppcCheckRegionId();
    }

    while (true) {

      logController.addLog("taskState ---$taskState");
      if (taskState == TaskState.stopped) {
        logController.addLog('⏹ 错误️ 任务已被停止！');
        break;
      }

      if (taskState == TaskState.error) {
        logController.addLog('⏹ 错误️ 任务已被暂停-请检查失败原由！');
        break;
      }

      final availableUsers = userController.users.where((user) =>
      user.isStart &&
          user.username != "暂未登录" &&
          user.apiKey != null &&
          user.apiKey!.isNotEmpty
      ).toList();

      if (availableUsers.isEmpty) {
        logController.addLog("❌ 没有可用账号");
        setErrorState();

        return;
      }

      // startDate = "2008-01-04";

      // 收集未完成任务并按地区和年份分组
      var uncompletedTasks = collectUncompletedTasksByRegionAndYear(baiduIndexLogic.data);
      addLogTasksByRegionAndYear(uncompletedTasks);
      bool hasError = false;
      List<Future<void> Function()> taskFunctions = []; // 改用函数列表


      // 遍历每个可用用户分配任务
      for (var user in availableUsers) {
        logController.addLog("user ---- $user");
        // 查找第一个有任务的地区和年份组合
        String? selectedRegion;
        String? selectedYear;
        List<TaskModel>? selectedTasks;

        // 查找可分配的任务
        for (var regionEntry in uncompletedTasks.entries) {
          for (var yearEntry in regionEntry.value.entries) {
            if (yearEntry.value.isNotEmpty) {
              selectedRegion = regionEntry.key;
              selectedYear = yearEntry.key;
              selectedTasks = yearEntry.value;
              break;
            }
          }
          if (selectedTasks != null) break;
        }

        // 如果找到可分配的任务
        if (selectedTasks != null && selectedTasks.isNotEmpty) {
          // 计算本次分配的任务数（不超过5个）
          int taskCount = 0;

          if (baiduIndexLogic.isAnyIndexActive == "brand"){
            taskCount = min(1, selectedTasks.length);
          }else{
            taskCount = min(5, selectedTasks.length);
          }
          var userTasks = selectedTasks.take(taskCount).toList();

          // 从未完成任务列表中移除已分配的任务
          selectedTasks.removeRange(0, taskCount);

          // 如果该地区该年份的任务已空，清理数据结构
          if (selectedTasks.isEmpty) {
            uncompletedTasks[selectedRegion]!.remove(selectedYear);
            if (uncompletedTasks[selectedRegion]!.isEmpty) {
              uncompletedTasks.remove(selectedRegion);
            }
          }

          logController.addLog('\n📋 分配任务给用户: ${user.username}',);
          logController.addLog('📍 地区: $selectedRegion', );
          logController.addLog('📅 年份: $selectedYear',);
          logController.addLog('📊 任务数: $taskCount', );

          // 处理任务
          logController.addLog("需要处理的任务详情-----${userTasks.length}");
          logController.addLog("地区-----$selectedRegion");
          logController.addLog("年份-----$selectedYear");


          // 打印分配详情
          addLogUserTaskAssignments(user, userTasks);
          // 将整个任务包装在函数中
          taskFunctions.add(() async {
            try {
              if (baiduIndexLogic.isAnyIndexActive == "brand"){
                await ppcProcessTask(userTasks, user);
              }else{
                await processTask(userTasks, user);
              }
              logController.addLog('✅ 地区-$selectedRegion -年份-$selectedYear - ${userTasks.map((task) => task.keyword).toList()} 任务完成');
            } catch (e) {
              // 错误处理
              hasError = true;
              setErrorState();
              logController.addLog('❌ 任务失败: $e');
              NotificationUtil.show(
                title: '任务失败',
                body: '当前任务失败。请查看原由！',
              );

              if (e.toString().contains('cookie失效') || e.toString().contains('账号异常')) {
                user.isStart = false;
                logController.addLog('❌ 任务失败️ 用户 ${user.username} 已被标记为不可用');
              }
              rethrow;
            }
          });


        }
      }




      // 等待当前批次所有任务完成
      if (taskFunctions.isNotEmpty) {
        logController.addLog('\n⏳ 等待当前批次任务完成...', );
        logController.addLog("并发处理任务-----$taskFunctions-----长度${taskFunctions.length}");
        await Future.wait(
          taskFunctions.map((fn) => fn()), // 在这里才真正执行函数
          eagerError: false,
        );
        // 添加延迟倒计时
        await delayWithCountdown(
          seconds: baiduIndexLogic.extractionInterval,
        );

        if (hasError) {
          logController.addLog('❌ 检测到任务失败，停止后续任务', );
          break;
        }
      } else {
        logController.addLog('✅ 成功️ 全部执行完成', );
        NotificationUtil.show(
          title: '任务状态',
          body: '任务已全部执行完成',
        );
        setInitialState();
        break;
      }

      // logController.addLog(json.encode(data.map((e) => e.toJson()).toList()));


    }
  }

  // 打印用户任务分配情况
  void addLogUserTaskAssignments(BaiDuUsers user, List<TaskModel> tasks) {
    logController.addLog('\n=== 用户任务分配详情 ===');
    logController.addLog('👤 用户: ${user.username}');
    logController.addLog('📋 分配任务数: ${tasks.length}');
    if (tasks.isNotEmpty) {
      logController.addLog('📍 地区: ${tasks[0].region}');
      logController.addLog('📅 年份: ${tasks[0].year}');
      logController.addLog('任务列表:');
      for (var task in tasks) {
        logController.addLog('  - ${task.keyword} (${task.drama.startData} ~ ${task.drama.endData})');
      }
    }
    logController.addLog('=== 分配详情结束 ===\n');
  }

  // 设置继续状态
  void setErrorState() {
    if (taskState == TaskState.running) {
      taskState = TaskState.error;
      baiduIndexLogic.update(['buttons']);
    }
  }
  //设置成开始状态
  void setInitialState() {
    taskState = TaskState.initial;
    baiduIndexLogic.update(['buttons']);
  }
  //设置成停止状态
  void setStoppedState() {
    taskState = TaskState.stopped;
    baiduIndexLogic.update(['buttons']);
  }

  // 打印任务分布情况
  void addLogTasksByRegionAndYear(Map<String, Map<String, List<TaskModel>>> tasksByRegionAndYear) {
    logController.addLog('\n=== 未完成任务统计 ===');
    tasksByRegionAndYear.forEach((region, yearTasks) {
      logController.addLog('\n🌍 地区: $region');
      yearTasks.forEach((year, tasks) {
        logController.addLog('  📅 $year年 (共${tasks.length}个任务):');
        for (var task in tasks) {
          logController.addLog('    - ${task.keyword} (${task.drama.startData} ~ ${task.drama.endData})');
        }
      });
    });
    logController.addLog('\n=== 统计结束 ===\n');
  }



  // 按地区和年份收集未完成任务 - 带缓存优化版本
  Map<String, Map<String, List<TaskModel>>> collectUncompletedTasksByRegionAndYear(List<DataModel> data) {
    // 计算数据哈希值来判断是否需要重新计算
    int currentHash = _calculateDataHash(data);

    // 如果缓存有效且数据没变，返回缓存结果
    if (_tasksCacheValid && _cachedTasks != null && _lastDataHash == currentHash) {
      return _cachedTasks!;
    }

    // 重新计算任务
    _cachedTasks = _computeUncompletedTasks(data);
    _lastDataHash = currentHash;
    _tasksCacheValid = true;

    return _cachedTasks!;
  }

  /// 计算数据哈希值
  int _calculateDataHash(List<DataModel> data) {
    int hash = 0;
    for (var dataModel in data) {
      if (dataModel.id == null) continue;

      hash ^= dataModel.region.hashCode;
      dataModel.data?.forEach((year, dramaList) {
        hash ^= year.hashCode;
        for (var drama in dramaList) {
          hash ^= drama.keyword.hashCode;
          hash ^= drama.isCompleted.hashCode;
        }
      });
    }
    return hash;
  }

  /// 实际计算未完成任务的方法
  Map<String, Map<String, List<TaskModel>>> _computeUncompletedTasks(List<DataModel> data) {
    // 外层Map的key是地区，内层Map的key是年份
    Map<String, Map<String, List<TaskModel>>> tasksByRegionAndYear = {};

    for (var dataModel in data) {
      if (dataModel.id == null) continue;

      // 初始化地区Map
      if (!tasksByRegionAndYear.containsKey(dataModel.region)) {
        tasksByRegionAndYear[dataModel.region!] = {};
      }

      dataModel.data?.forEach((year, dramaList) {
        // 初始化年份List
        if (!tasksByRegionAndYear[dataModel.region]!.containsKey(year)) {
          tasksByRegionAndYear[dataModel.region]![year] = [];
        }

        for (var drama in dramaList) {
          if (drama.isCompleted != true) {
            tasksByRegionAndYear[dataModel.region]![year]!.add(TaskModel(
              dataModel: dataModel,
              keyword: drama.keyword!,
              drama: drama,
              year: year,
              cityId: dataModel.id!,
              region: dataModel.region!,
            ));
          }
        }
      });
    }

    return tasksByRegionAndYear;
  }

  /// 标记任务完成，清除缓存
  void markTaskCompleted() {
    _tasksCacheValid = false;
    _cachedTasks = null;
  }


  // 检查是否可以开始任务
  bool checkCanStart() {
    // 检查关键词
    if (baiduIndexLogic.keyWords.isEmpty) {
      logController.addLog("❌ 请先添加关键词");
      return false;
    }



    // 检查日期范围
    if (baiduIndexLogic.startDate.isEmpty || baiduIndexLogic.endDate.isEmpty) {
      logController.addLog("❌ 请选择日期范围");
      return false;
    }

    // 将日期字符串转换为DateTime对象进行比较
    try {
      DateTime start = DateTime.parse(baiduIndexLogic.startDate);
      DateTime end = DateTime.parse(baiduIndexLogic.endDate);
      if (start.isAfter(end)) {
        logController.addLog("❌ 开始日期不能大于结束日期");
        return false;
      }
    } catch (e) {
      logController.addLog("❌ 日期格式错误");
      return false;
    }

    // 检查是否选择了指数类型
    if (!hasSelectedIndexType()) {
      logController.addLog("❌ 请至少选择一种指数类型");
      return false;
    }

    // 检查是否有选择地区
    if (flattenCheckedNodes(baiduIndexLogic.area_data).isEmpty) {
      logController.addLog("❌ 请选择地区");
      return false;
    }

    //数据处理

    List<TreeNode> treeNode =  flattenCheckedNodes(baiduIndexLogic.area_data);
    List<YearDetails> yearDetails = daysInEachYear(baiduIndexLogic.startDate, baiduIndexLogic.endDate);
    baiduIndexLogic.data.clear();
    Map<String, DataModel> regionMap = {};
    for (var node in treeNode) {
      // 先插入城市
      var dataModel = DataModel(region: node.name, id: node.id.toString());

      // 存储到 regionMap，使用城市名作为键，便于快速访问
      regionMap[node.name] = dataModel;

      // 动态生成关键词，一个城市对应多个关键词
      for (var keyWord in baiduIndexLogic.keyWords) {
        // 创建一个空的年份数据
        Map<String, List<Drama>> yearData = {};

        // 根据 yearDetails 来生成每年的 Drama
        for (var yearDetail in yearDetails) {
          String yearKey = yearDetail.start.substring(0, 4); // 获取年份，如 "2023"

          // 如果年份数据为空，则初始化
          if (!yearData.containsKey(yearKey)) {
            yearData[yearKey] = [];
          }

          // 为每个关键词生成 Drama 数据
          yearData[yearKey]!.add(Drama(
            keyword: keyWord,  // 关键词
            startData: yearDetail.start,
            endData: yearDetail.end,
            isCompleted: false,
            all: [],
            pc: [],
            wise: [],
          ));
        }

        // 将生成的年份数据添加到 dataModel 的 data 中
        dataModel.data ??= {};  // 确保 data 是初始化的
        yearData.forEach((year, dramas) {
          if (dataModel.data!.containsKey(year)) {
            // 如果已有年份数据，则合并新的 drama
            dataModel.data![year]!.addAll(dramas);
          } else {
            // 否则直接添加
            dataModel.data![year] = dramas;
          }
        });
      }
    }


    // 最终数据存储在 regionMap 中，您可以根据需要处理它
    baiduIndexLogic.data = regionMap.values.toList();

    return true;
  }


  ppcCheckRegionId ()async{
    Map<String, String> headers = {
      'Accept': 'application/json, text/plain, */*',
      'Accept-Encoding': 'gzip, deflate, br',
//     'Cipher-Text':"""
// 1719227393545_1719297883463_ru+UprB9gpwT7WwiWAVjCtCYF62iacritHFdi6xzdQoN77ErmFRqYmuQ8xwK2qzOPkkPNx4Kv6Or2YzDFcLMcqk5MXxWGoKIXIzzAMi8mHpVJRCMRswfXd1QWapnnAQsIbw4E2HRHIdBpfH6+uINhE4r5InHgSLkjjioFdGNAMps0ZNoa+S9dJgEzlItL46LBMdZQBeryvasC8GUNlUNlka8wf9uxFTlD/LsOc/qsgn/Uj+lMzTjNqMomYWiXVDqxntXKEW7TVEVva8AXzq4yQzlSsuL9xjFWqTydkoXtVQ6zgYgoB0IIooW+/jgWQQrzNqpOjbbMlXDpkFJDNZ+LnwlvEfSAEWKJTbzEbWvdP8UK3wee9zTUlOWtL7ATPjfDtLQEVt0XLCoGEy/OamDI09GfwkcOG2e/B2bdwtNsDu/N2fa2NZkU3uvObHvAzCt""",
      'Connection': 'close',
      'Host': 'index.baidu.com',
      'Referer': 'https://index.baidu.com/v2/main/index.html',
      'User-Agent':"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.438.400 QQBrowser/13.0.6071.400"
    };
    logController.addLog("$baiduIndexLogic.keyWords");

    for(var keyword in baiduIndexLogic.keyWords){
      logController.addLog('📍 正在查找品牌词RegionId: $keyword ', );

      logController.addLog("keyword--$keyword");

      String wordSugUrl = "https://index.baidu.com/insight/word/sug";

      var response = await httpClientUtil.post(url: wordSugUrl,headers: headers,data:{"words":[keyword],"source":"pc_home"});
      logController.addLog("response --- ${response['data']}");
      // 获取 data 中的 result 列表
      List<dynamic> result = response['data']['result'];

      // 遍历 result 列表
      for (var item in result) {
        // 检查 word 是否匹配
        if (item['word'] == keyword && item['type'] == "brand") {
          baiduIndexLogic.ppcRegionId[item["word"]] = item['brandInfo']['id'];
        }
      }
    }
    logController.addLog('✅ 成功 ------查找结束开始提取品牌词-----', );

  }


  // 单个任务处理函数
  Future<void> ppcProcessTask(
      List<TaskModel> taskModel,
      BaiDuUsers user,
      ) async {


    logController.addLog("------------------------");
    logController.addLog("-----单个任务处理函数------");
    logController.addLog("------------------------");



    String startData = taskModel.map((task) => task.drama.startData).toSet().toString().replaceAll("{", "").replaceAll("}", "");
    String endData = taskModel.map((task) => task.drama.endData).toSet().toString().replaceAll("{", "").replaceAll("}", "");
    String cityId = taskModel.map((task) => task.cityId).toSet().toString().replaceAll("{", "").replaceAll("}", "");

    List<List<String>> keywords = taskModel
        .map((task) => [task.keyword.toString()])
        .toList();


    Map<String, String> headers = {
      'Accept': 'application/json, text/plain, */*',
      'Accept-Encoding': 'gzip, deflate, br',
//     'Cipher-Text':"""
// 1719227393545_1719297883463_ru+UprB9gpwT7WwiWAVjCtCYF62iacritHFdi6xzdQoN77ErmFRqYmuQ8xwK2qzOPkkPNx4Kv6Or2YzDFcLMcqk5MXxWGoKIXIzzAMi8mHpVJRCMRswfXd1QWapnnAQsIbw4E2HRHIdBpfH6+uINhE4r5InHgSLkjjioFdGNAMps0ZNoa+S9dJgEzlItL46LBMdZQBeryvasC8GUNlUNlka8wf9uxFTlD/LsOc/qsgn/Uj+lMzTjNqMomYWiXVDqxntXKEW7TVEVva8AXzq4yQzlSsuL9xjFWqTydkoXtVQ6zgYgoB0IIooW+/jgWQQrzNqpOjbbMlXDpkFJDNZ+LnwlvEfSAEWKJTbzEbWvdP8UK3wee9zTUlOWtL7ATPjfDtLQEVt0XLCoGEy/OamDI09GfwkcOG2e/B2bdwtNsDu/N2fa2NZkU3uvObHvAzCt""",
      'Connection': 'close',
      'Host': 'index.baidu.com',
      'Referer': 'https://index.baidu.com/v2/main/index.html',
      'User-Agent':"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.438.400 QQBrowser/13.0.6071.400"
    };


    Map<String, dynamic>? qs = {
      'regionId': cityId,
      'startDate': startData,
      'endDate': endData,
      "entityId":baiduIndexLogic.ppcRegionId[keywords[0][0]],
      "stat": true
    };


    headers['Cookie'] = user.cookie;

    // GET请求示例
    try {
      final response = await httpClientUtil.post<Map<String, dynamic>>(
        url: PPCApi,
        headers: headers,
        data: qs,
        maxRetries: 3,
        proxyAddress: user.isProxy?'${user.proxyAddress}:${user.proxyPort}':null, // 动态设置代理
      );

      logController.addLog("response --- $response");

      var status = response?["status"];
      var message = response?["message"];

      if (status.toString() == '10001'){
        throw Exception(message);
      }

      List encryptedData = response?["data"];

      for (var tasks in taskModel) {
        logController.addLog("tasks.drama.keyword---${tasks.drama.keyword}");
        logController.addLog("word---${keywords[0][0]}");
        if(tasks.drama.keyword == keywords[0][0]){
          tasks.drama.all = encryptedData.map((item) => item['value'] as int).toList();
          tasks.drama.pc = encryptedData.map((item) => item['value'] as int).toList();
          tasks.drama.wise = encryptedData.map((item) => item['value'] as int).toList();
          tasks.drama.isCompleted = true;
          // 任务完成时清除缓存
          markTaskCompleted();
          logController.addLog("本次请求成功结束");
        }
      }



    } catch (e) {
      NotificationUtil.show(
        title: '任务失败',
        body: '当前任务失败。请查看原由！',
      );
      user.isError = true;
      user.isStart = false;
      logController.addLog('错误1: $e');
      setErrorState();
      logController.addLog("❌ 错误-账号:${user.username}---$e");

      baiduIndexLogic.update(['list', "two"]);

    }

  }


  // 单个任务处理函数
  Future<void> processTask(
      List<TaskModel> taskModel,
      BaiDuUsers user,
      ) async {


    logController.addLog("------------------------");
    logController.addLog("-----单个任务处理函数------");
    logController.addLog("------------------------");



    String startData = taskModel.map((task) => task.drama.startData).toSet().toString().replaceAll("{", "").replaceAll("}", "");
    String endData = taskModel.map((task) => task.drama.endData).toSet().toString().replaceAll("{", "").replaceAll("}", "");
    String cityId = taskModel.map((task) => task.cityId).toSet().toString().replaceAll("{", "").replaceAll("}", "");

    List<List<String>> keywords = taskModel
        .map((task) {
      if (task.keyword.contains('+')) {
        // 如果包含 "+"，按 "+" 分割并为每个部分生成一个 Map，然后转换成 JSON 字符串
        return task.keyword.split('+').map((part) {
          var map = {'name': part.toString(), 'wordType': 1};
          return json.encode(map);  // 每个 Map 被 json.encode 转换为 JSON 字符串
        }).toList();
      } else {
        // 如果没有 "+"，直接生成一个包含一个 JSON 字符串的列表
        var map = {'name': task.keyword.toString(), 'wordType': 1};
        return [json.encode(map)];
      }
    }).toList();

    logController.addLog("keywords---$keywords");
    List<String> cipherTextKeyWords = taskModel.map((task) => task.keyword).toList();


    Map<String, String> headers = {
      'Accept': 'application/json, text/plain, */*',
      'Accept-Encoding': 'gzip, deflate, br',
//     'Cipher-Text':"""
// 1719227393545_1719297883463_ru+UprB9gpwT7WwiWAVjCtCYF62iacritHFdi6xzdQoN77ErmFRqYmuQ8xwK2qzOPkkPNx4Kv6Or2YzDFcLMcqk5MXxWGoKIXIzzAMi8mHpVJRCMRswfXd1QWapnnAQsIbw4E2HRHIdBpfH6+uINhE4r5InHgSLkjjioFdGNAMps0ZNoa+S9dJgEzlItL46LBMdZQBeryvasC8GUNlUNlka8wf9uxFTlD/LsOc/qsgn/Uj+lMzTjNqMomYWiXVDqxntXKEW7TVEVva8AXzq4yQzlSsuL9xjFWqTydkoXtVQ6zgYgoB0IIooW+/jgWQQrzNqpOjbbMlXDpkFJDNZ+LnwlvEfSAEWKJTbzEbWvdP8UK3wee9zTUlOWtL7ATPjfDtLQEVt0XLCoGEy/OamDI09GfwkcOG2e/B2bdwtNsDu/N2fa2NZkU3uvObHvAzCt""",
      'Connection': 'close',
      'Host': 'index.baidu.com',
      'Referer': 'https://index.baidu.com/v2/main/index.html',
      'User-Agent':"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.438.400 QQBrowser/13.0.6071.400"
    };


    Map<String, dynamic>? qs = {
      'area': cityId,
      'word': '$keywords',
      'startDate': startData,
      'endDate': endData
    };


    logController.addLog("qs------$qs");

    headers['Cipher-Text'] = getCipherText(cipherTextKeyWords,user.apiKeyTime,user.apiKey);
    headers['Cookie'] = user.cookie;

    // GET请求示例
    try {
      final response = await httpClientUtil.get<Map<String, dynamic>>(
        url: baiduIndexLogic.isAnyIndexActive == "consult" ? FeedSearchApi : SearchApi,
        headers: headers,
        queryParams: qs,
        maxRetries: 3,
        proxyAddress: user.isProxy?'${user.proxyAddress}:${user.proxyPort}':null, // 动态设置代理
      );

      logController.addLog("response --- $response");

      var status = response?["status"];
      var message = response?["message"];

      if (status.toString() == '10001'){
        throw Exception(message);
      }

      Map encryptedData = response?["data"];

      if (encryptedData.isNotEmpty){
        var status = response?['status'];
        if(status.toString() == '0'){

          headers.remove('Cipher-Text');
          String uniqid = response?["data"]["uniqid"];
          // logController.addLog(encryptedData);
          String ptbk_url = 'https://index.baidu.com/Interface/ptbk?uniqid=$uniqid';
          String ptbk = "";

          if (user.decryptCache.containsKey(uniqid)) {
            ptbk = user.decryptCache[uniqid]!;
          } else {

            var res = await httpClientUtil.get<Map<String, dynamic>>(
              url: ptbk_url,
              queryParams: {},
              headers: headers,
              proxyAddress: user.isProxy?'${user.proxyAddress}:${user.proxyPort}':null, // 动态设置代理
            );
            ptbk = res!['data'];
            user.decryptCache[uniqid] = ptbk;
          }


          if (baiduIndexLogic.isAnyIndexActive == "consult"){
            for (var userIndexesData in encryptedData['index']) {
              String word = userIndexesData['key'].map((item) => item['name']).join('+');
              String encryptedDataAll = userIndexesData['data'];


              List<DateTime> timestampList = List<DateTime>.generate(
                DateTime
                    .parse(endData)
                    .difference(DateTime.parse(startData))
                    .inDays + 1,
                    (index) =>
                    DateTime.parse(startData).add(Duration(days: index)),
              );

              List<String> dateList = timestampList
                  .map((timestamp) => DateFormat('yyyy-MM-dd').format(timestamp))
                  .toList();

              List<int> decryptedDataAll = decrypt(ptbk, encryptedDataAll)
                  .split(',')
                  .map((data) => fillZero(data))
                  .toList();

              List<int> generateZeroArray(int length) {
                // 创建一个长度为 `length` 的列表，每个元素都是 `[0]`
                return List.generate(length, (_) => 0);
              }
              for (var tasks in taskModel) {
                logController.addLog("tasks.drama.keyword---${tasks.drama.keyword}");
                logController.addLog("word---${word}");
                if(tasks.drama.keyword == word){
                  tasks.drama.all = dateList.length >decryptedDataAll.length ?generateZeroArray(dateList.length):decryptedDataAll;
                  tasks.drama.pc = generateZeroArray(dateList.length);
                  tasks.drama.wise = generateZeroArray(dateList.length);
                  tasks.drama.isCompleted = true;
                  // 任务完成时清除缓存
                  markTaskCompleted();
                  logController.addLog("本次请求成功结束");
                }
              }

            }


          }else{
            for (var userIndexesData in encryptedData['userIndexes']) {
              String word = userIndexesData['word'].map((item) => item['name']).join('+');
              print(word);

              String encryptedDataAll = userIndexesData['all']['data'];
              String encryptedDataPc = userIndexesData['pc']['data'];
              String encryptedDataWise = userIndexesData['wise']['data'];

              if(encryptedDataWise.isEmpty){
                encryptedDataAll = encryptedDataPc;
              }
              if(encryptedDataPc.isEmpty){
                encryptedDataAll = encryptedDataWise;
              }
              if(encryptedDataAll.isEmpty&&encryptedDataPc.isEmpty&&encryptedDataWise.isEmpty){
                encryptedDataAll = "";
                encryptedDataPc = "";
                encryptedDataWise = "";
              }

              List<DateTime> timestampList = List<DateTime>.generate(
                DateTime
                    .parse(endData)
                    .difference(DateTime.parse(startData))
                    .inDays + 1,
                    (index) =>
                    DateTime.parse(startData).add(Duration(days: index)),
              );

              List<String> dateList = timestampList
                  .map((timestamp) =>
                  DateFormat('yyyy-MM-dd').format(timestamp))
                  .toList();


              List<int> decryptedDataAll = decrypt(ptbk, encryptedDataAll)
                  .split(',')
                  .map((data) => fillZero(data))
                  .toList();
              List<int> decryptedDataPc = decrypt(ptbk, encryptedDataPc)
                  .split(',')
                  .map((data) => fillZero(data))
                  .toList();
              List<int> decryptedDataWise = decrypt(
                  ptbk, encryptedDataWise)
                  .split(',')
                  .map((data) => fillZero(data))
                  .toList();

              List<int> generateZeroArray(int length) {
                // 创建一个长度为 `length` 的列表，每个元素都是 `[0]`
                return List.generate(length, (_) => 0);
              }
              for (var tasks in taskModel) {
                logController.addLog("tasks.drama.keyword---${tasks.drama.keyword}");
                logController.addLog("word---${word}");
                if(tasks.drama.keyword == word){
                  tasks.drama.all = dateList.length >decryptedDataAll.length ?generateZeroArray(dateList.length):decryptedDataAll;
                  tasks.drama.pc = dateList.length >decryptedDataPc.length ?generateZeroArray(dateList.length):decryptedDataPc;
                  tasks.drama.wise = dateList.length >decryptedDataWise.length ?generateZeroArray(dateList.length):decryptedDataWise;
                  tasks.drama.isCompleted = true;
                  // 任务完成时清除缓存
                  markTaskCompleted();
                  logController.addLog("本次请求成功结束");
                }
              }

            }
          }


        }else{
          logController.addLog("object-----$response");
          //    抛出异常
          // Handle case when the status is not 0 (failure or unexpected status)
          throw Exception(message);
        }
      }

    } catch (e) {
      NotificationUtil.show(
        title: '任务失败',
        body: '当前任务失败。请查看原由！',
      );
      user.isError = true;
      user.isStart = false;
      logController.addLog('错误1: $e');
      setErrorState();
      logController.addLog("❌ 错误-账号:${user.username}---$e");

      baiduIndexLogic.update(['list', "two"]);

    }

  }


  /// 延迟倒计时函数 - 优化版本
  Future<void> delayWithCountdown({required String? seconds,}) async {
    try {
      // 确定延迟时间
      int delaySeconds = 1;
      if (seconds != null && seconds.isNotEmpty) {
        delaySeconds = int.tryParse(seconds) ?? 1;
        logController.addLog('⚡ 设置任务间隔: $delaySeconds 秒');
      } else {
        logController.addLog('⚡ 使用默认间隔: 1秒');
      }

      // 如果延迟时间很短，直接等待，不显示倒计时
      if (delaySeconds <= 3) {
        await Future.delayed(Duration(seconds: delaySeconds));
        logController.addLog('✨ 开始执行新的任务...');
        return;
      }

      // 对于较长的延迟，只在关键时间点显示进度
      int remainingSeconds = delaySeconds;
      while (remainingSeconds > 0) {
        // 只在特定时间点显示进度，减少UI更新频率
        if (remainingSeconds == delaySeconds ||
            remainingSeconds % 10 == 0 ||
            remainingSeconds <= 5) {
          logController.addLog('⏱️ 等待中: 还剩 $remainingSeconds 秒...');
        }

        await Future.delayed(Duration(seconds: 1));
        remainingSeconds--;
      }

      // 倒计时结束
      logController.addLog('✨ 开始执行新的任务...');

    } catch (e) {
      logController.addLog('❌ 任务间隔出错: $e');
    }
  }


  // 检查是否选择了指数类型
  bool hasSelectedIndexType() {
    return baiduIndexLogic.AnyIndexActives.isNotEmpty;  // 假设你有一个存储选中指数类型的列表
  }

  // 遍历所有节点，将 isChecked == true 的节点放入列表中
  List<TreeNode> flattenCheckedNodes(List<TreeNode> nodes) {
    List<TreeNode> flatList = [];

    void traverse(TreeNode node) {
      if (node.isChecked) {
        flatList.add(node);
      }
      for (var child in node.children) {
        traverse(child);
      }
    }

    for (var node in nodes) {
      traverse(node);
    }

    return flatList;
  }


  //  解密
  // 解密函数
  String decrypt(String ptbk, String encryptedData) {
    // 这里假设解密过程仅为演示目的，请根据实际情况实现
    if (ptbk.isEmpty || encryptedData.isEmpty) {
      return "";
    }
    int n = ptbk.length ~/ 2;
    Map<String, String> d = {for (int o = 0; o < n; o++) ptbk[o]: ptbk[n + o]};
    List<String> decryptedData =
    encryptedData.split('').map((data) => d[data] ?? "").toList();
    return decryptedData.join("");
  }

// 数据填充零
  int fillZero(String data) {
    return data.isEmpty ? 0 : int.parse(data);
  }


}