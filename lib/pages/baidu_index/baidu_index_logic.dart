import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:bd/pages/baidu_index/controllers/baidu_index_data_controller%20.dart';
import 'package:bd/pages/baidu_index/controllers/baidu_index_task_controller%20.dart';
import 'package:bd/utils/notification_util.dart';
import 'package:bd/utils/store_util.dart';
import 'package:csv/csv.dart';
import 'package:bd/utils/encrypt.dart';
import 'package:bd/model/baidu_user_model.dart';
import 'package:bd/model/data_model.dart';
import 'package:bd/model/task_result_model.dart';
import 'package:bd/utils/http_client.dart';
import 'package:bd/utils/toast_util.dart';
import 'package:file_picker/file_picker.dart';
import 'package:excel/excel.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:puppeteer/protocol/network.dart';
import 'package:puppeteer/puppeteer.dart';
import 'package:http/http.dart' as http;
import 'package:dio/dio.dart' as diox;


import '../../model/baidu_model.dart';
import '../../utils/date.dart';
import '../../model/city_model.dart';
import 'controllers/baidu_index_log_controller .dart';
import 'controllers/baidu_index_user_controller.dart';  // 用于获取原始 JS 文件内容



class BaiduIndexLogic extends GetxController {

  LogController get logController => Get.find<LogController>();
  UserController get userController => Get.find<UserController>();
  TaskController get taskController => Get.find<TaskController>();
  DataController get dataController => Get.find<DataController>();
  // 创建实例
  final httpClientUtil = HttpClientUtil();


  String pt_proxy_url ="";

  bool isZDProxy = false;
  //  日志相关
  List<String> logs = [];
  // 添加日志的ScrollController
  final ScrollController logScrollController = ScrollController();



  bool isStarted = false;  // 只需要一个状态标记

  //关键词列表
  List<String>  keyWords = [];



  //开始日期
  String startDate = "";
  //结束日期
  String endDate = "";




  //提取间隔
  String extractionInterval = "";

  //文件存放
  int fileStorageIndex = 2;
  List<bool> fileStorageOptions = [false, false, false];

  //自动保存与邮件发送配置
  bool enableAutoSave = false;  // 是否启用自动保存
  String currentSaveTimestamp = "";  // 当前保存的时间戳文件夹名
  String recipientEmail = "";  // 收件人邮箱

  //选择什么指数
  String isAnyIndexActive = "";
  List AnyIndexActives = [];

  //县级市
  List<String> county_level_city = [
    "西双版纳",
    "德宏",
    "怒江",
    "迪庆",
    "石河子",
    "昌吉",
    "阿克苏",
    "博尔塔拉",
    "阿勒泰",
    "喀什",
    "和田",
    "巴音郭楞",
    "伊犁",
    "塔城",
    "五家渠",
    "阿拉尔",
    "图木舒克",
    "湘西",
    "巢湖",
    "阿拉善盟",
    "锡林郭勒盟",
    "兴安盟",
    "临夏",
    "甘南",
    "万宁",
    "琼海",
    "东方",
    "五指山",
    "文昌",
    "陵水",
    "澄迈",
    "乐东",
    "临高",
    "定安",
    "昌江",
    "屯昌",
    "保亭",
    "白沙",
    "琼中",
    "黔南",
    "黔东南",
    "黔西南",
    "海西",
    "玉树",
    "青海海南",
    "海北",
    "黄南",
    "果洛",
    "甘孜",
    "阿坝",
    "神农架",
    "潜江",
    "延边",
    "仙桃",
    "恩施",
    "红河",
    "济源",
    "莱芜",
    "文山",
    "天门",
    "楚雄",
    "凉山"];

  List<TreeNode> area_data = [
    TreeNode("全国", 0,[])
  ];

  final  List<TreeNode> selected_area_data = [];

  //关键词输入框控制器
  TextEditingController gjcTextEditingController = TextEditingController();

  Future<Map<String, dynamic>> loadJsonFromAssets(String filePath) async {
    String jsonString = await rootBundle.loadString(filePath);
    return jsonDecode(jsonString);
  }


  //数据
  List<DataModel>  data = [];
  Map<String,dynamic> ppcRegionId = {};




  bool sjjz = true;
  bool sjpj = false;

  bool r = true;
  bool z = false;
  bool y = false;
  bool n = false;

  Timer? _proxyCheckTimer;
  
  @override
  void onInit() async{
    super.onInit();
    await loadAreaData("gjc");
    startProxyCheckTimer();
  }

  @override
  void onClose() {
    _proxyCheckTimer?.cancel();
    _proxyCheckTimer = null; // 显式置空
    super.onClose();
  }

  void startProxyCheckTimer() {
    _proxyCheckTimer?.cancel();
    // 每秒检查一次代理状态和更新UI
    _proxyCheckTimer = Timer.periodic(const Duration(seconds: 1), (timer) async {
      final now = DateTime.now();
      
      for (int i = 0; i < userController.users.length; i++) {
        var user = userController.users[i];
        if (user.isProxy && user.proxyStartTime != null) {
          // 更新UI显示
          update(['proxy_timer_$i']);
          
          // 检查代理是否过期
          if (user.proxyValidTime != null) {
            final expiryTime = user.proxyStartTime!.add(Duration(minutes: user.proxyValidTime!));
            if (now.isAfter(expiryTime)) {
              user.isProxy = false;
              user.isStart = false;
              logController.addLog("× 错误-账号:${user.username}---代理IP已过期");
              showToast("账号${user.username}的代理已过期，已自动关闭");

             if(isZDProxy){
               await autoRenewProxyForUser(user);
             }

              update(['two','logs']);
            }
          }
        }
      }
    });
  }


  Future<void> autoRenewProxyForUser(BaiDuUsers user) async {
    try {
      logController.addLog("🚀 开始代理续期 - 账号: ${user.username}");

      // 发起代理请求
      final response = await httpClientUtil.get(
        url: pt_proxy_url,
      ).catchError((e) {
        throw Exception('代理API请求失败: $e');
      });

      // 解析并验证代理
      final proxy = _parseProxyResponse(response.toString());
      _validateProxyConfiguration(proxy);

      // 更新用户数据
      _updateUserProxyInfo(user, proxy);

      logController.addLog("✅ 成功续期 - 账号: ${user.username} 代理: ${proxy.ip}:${proxy.port}");
    } catch (e) {
      logController.addLog("❌ 续期失败 - 账号: ${user.username} 原因: ${e.toString()}");
      rethrow;
    }
  }

// 解析代理响应
  ProxyConfig _parseProxyResponse(String response) {
    final configs = response.split('\n')
        .map((line) => line.trim())
        .where((line) => line.isNotEmpty)
        .map(ProxyConfig.fromString)
        .whereType<ProxyConfig>()
        .toList();

    if (configs.isEmpty) throw Exception('无有效代理配置');
    return configs.first;
  }

// 代理有效性验证
  void _validateProxyConfiguration(ProxyConfig proxy) {
    final ipValid = RegExp(r'^(\d{1,3}\.){3}\d{1,3}$').hasMatch(proxy.ip);
    final port = int.tryParse(proxy.port) ?? 0;

    if (!ipValid) throw Exception('无效IP格式: ${proxy.ip}');
    if (port <= 0 || port > 65535) throw Exception('无效端口: ${proxy.port}');
  }

// 更新用户信息
  void _updateUserProxyInfo(BaiDuUsers user, ProxyConfig proxy) {
    user
      ..proxyAddress = proxy.ip
      ..proxyPort = proxy.port
      ..proxyUsername = proxy.username
      ..proxyPassword = proxy.password
      ..proxyStartTime = DateTime.now()
      ..isProxy = true
      ..isStart = true;

  }

  Future<void> loadAreaData(String type) async {
    // 1. 加载 JSON 数据
    Map<String, dynamic> provinces = await loadJsonFromAssets('assets/provinces.json');
    provinces = provinces[type];
    Map<String, dynamic> cityShip = await loadJsonFromAssets('assets/cityShip.json');
    cityShip = cityShip[type];

    // 2. 添加省份到 area_data
    for (var entry in provinces.entries) {
      area_data[0].children.add(TreeNode(entry.value, int.parse(entry.key), [])); // 初始化 children 为 []
    }

    // 3. 添加城市到相应的省份
    for (var area_data_item in area_data[0].children) {
      List? cityShips = cityShip[area_data_item.id.toString()]; // 使用 List? 来接收可能为 null 的值

      // 检查 cityShips 是否为 null，且是否不为空
      if (cityShips != null && cityShips.isNotEmpty) {
        for (var cityShips_item in cityShips) {
          // 确保 cityShips_item 具有正确的属性
          area_data_item.children!.add(TreeNode(cityShips_item['label'], int.parse(cityShips_item['value']),[])); // 使用字典访问
        }
      }else{
        area_data_item.children=[];
      }
    }

    // 打印 children 的长度
    update(["now"]);
    // 4. 打印省份和城市
    // for (var area_data_item in area_data[0].children) {
    //   // 检查 children 是否不为空
    //   if (area_data_item.children.isNotEmpty) {
    //     for (var area_data_item_children_item in area_data_item.children) {
    //       logController.addLog("省份----${area_data_item.name}-----城市------${area_data_item_children_item.name}");
    //     }
    //   }
    // }
  }

  void setAllNodesUnchecked(TreeNode node) {
    // 将当前节点的 isChecked 设为 false
    node.isChecked = false;

    // 递归处理所有子节点
    for (TreeNode child in node.children) {
      setAllNodesUnchecked(child);
    }
  }

  void setAllNodesUncheckedInList(List<TreeNode> nodeList) {
    for (TreeNode node in nodeList) {
      setAllNodesUnchecked(node);
    }
  }

  void checkCountyLevelCities(List<TreeNode> nodes,) async{
    Map<String, dynamic> provinces = await loadJsonFromAssets('assets/provinces.json');

    for (var node in nodes) {
      // 判断当前节点是否在县级市列表中
      if (d_city_s.contains(node.name)) {
        // 执行你需要的操作，例如将 isChecked 设置为 true
      } else {
        node.isChecked = true;




      }


      for (var entry in provinces.entries) {
        if (entry.value == node.name||  node.name == "吉林" || node.name == "全国") {
          node.isChecked = false;
          // 执行你需要的操作，例如将 isChecked 设置为 true
        }
      }

      // 如果该节点有子节点，递归处理
      if (node.children.isNotEmpty) {
        checkCountyLevelCities(node.children,);
      }
    }
  }



  void checkHandMovement(List<TreeNode> nodes,city) {
    for (var node in nodes) {
      // 判断当前节点是否在县级市列表中
      if (city.contains(node.name)) {
        // 执行你需要的操作，例如将 isChecked 设置为 true
        node.isChecked = true;
      } else {
      }

      // 如果该节点有子节点，递归处理
      if (node.children.isNotEmpty) {
        checkHandMovement(node.children,city);
      }
    }
  }

  void checkPrefectureLevelCity(List<TreeNode> nodes,) async{
    Map<String, dynamic> provinces = await loadJsonFromAssets('assets/provinces.json');
    for (var node in nodes) {
      // 判断当前节点是否在地级市列表中
      if (d_city_s.contains(node.name)) {
        // 执行你需要的操作，例如将 isChecked 设置为 true
          if(node.id == 922) {

          }else{
            node.isChecked = true;
          }

      } else {

      }


      // for (var entry in provinces.entries) {
      //   if (node.name == "吉林" || node.name == "全国") {
      //     node.isChecked = false;
      //     // 执行你需要的操作，例如将 isChecked 设置为 true
      //   }
      // }

      // 如果该节点有子节点，递归处理
      if (node.children.isNotEmpty) {
        checkPrefectureLevelCity(node.children,);
      }
    }
  }

  List<List<T>> partition<T>(List<T> list, int size) {
    List<List<T>> partitions = [];

    for (int i = 0; i < list.length; i += size) {
      partitions.add(list.sublist(i, i + size > list.length ? list.length : i + size));
    }

    return partitions;
  }

  Future<String?> pickSpreadsheetFile() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      dialogTitle: "请选择文件",
      type: FileType.custom,
      allowedExtensions: ["xlsx", "xls", "csv"], // 允许的表格文件类型
      allowMultiple: false,
    );

    if (result != null) {
      String? path = result.files.single.path;
      return path;
    } else {
      return null;
    }
  }

  Future<List<dynamic>> readExcelFile(String filePath) async {
    List<dynamic> firstColumnData = [];

    try {
      var bytes = File(filePath).readAsBytesSync();
      var excel = Excel.decodeBytes(bytes);

      // 输出Excel文件内容
      for (var table in excel.tables.keys) {
        var sheet = excel.tables[table]!;
        // 使用 sheet.rows 获取行数据并进行迭代处理
        for (var row in sheet.rows) {
          var value = row.isNotEmpty ? row.first?.value : '空行';
          firstColumnData.add(value); // 添加每一行的第一列数据到数组中
        }
      }
    } catch (e) {
      logController.addLog('读取Excel文件失败：$e');
    }

    return firstColumnData;
  }


  String formatDateTime(DateTime dateTime) {
    final DateFormat formatter = DateFormat('yyyy年M月d日 H点m分');
    return formatter.format(dateTime);
  }

  // 生成时间戳文件夹名称
  String generateTimestamp() {
    final now = DateTime.now();
    final DateFormat formatter = DateFormat('yyyy-MM-dd_HH-mm-ss');
    return formatter.format(now);
  }


  int countCheckedNodes(List<TreeNode> nodes) {
    int count = 0;

    for (var node in nodes) {
      if (node.isChecked) {
        count += 1; // 如果当前节点被选中，则计入数量
      }
      if (node.children.isNotEmpty) {
        count += countCheckedNodes(node.children); // 递归检查子节点
      }
    }

    return count;
  }




  // 检查是否有正在运行的任务
  bool hasRunningTask() {
    return isStarted && hasActiveRequests();
  }

  // 检是否有选择区
  bool hasSelectedAreas() {
    // 检查地区选择逻辑
    return taskController.flattenCheckedNodes(area_data).isNotEmpty;
  }



  // 检查是否有活跃的请求
  bool hasActiveRequests() {
    // 检查是否有正在进行的请求
    return true; // 根据实际情况实现
  }





  // 检查是否选择了地区
  bool _hasSelectedAreas() {
    return area_data.any((area) => 
      area.isChecked || 
      (area.children?.any((child) => child.isChecked) ?? false)
    );
  }




  // 开始任务
  Future<void> onBdStart() async{
    logController.addLog('✅ 成功️ 开始任务');
    if (!taskController.checkCanStart()) return;
    taskController.taskState = TaskState.running;
    update(['buttons']);
    try {
      await taskController.distributeTasksToUsers();
    } catch (e) {
      logController.addLog('任务分发失败: $e');
      taskController.taskState = TaskState.error;
    }
    
    update(['buttons']);
  }


   diox.Dio dioHttp = diox.Dio() ;

  // 更新数据
  // drama.all = results['all'];
  // drama.pc = results['pc'];
  // drama.wise = results['wise'];
  // drama.isCompleted = true;


  // 按年份收集未完成任务
  Map<String, List<TaskModel>> collectUncompletedTasksByYear(List<DataModel> data) {
    Map<String, List<TaskModel>> tasksByYear = {};

    for (var dataModel in data) {
      if (dataModel.id == null) continue;

      dataModel.data?.forEach((year, dramaList) {
        if (!tasksByYear.containsKey(year)) {
          tasksByYear[year] = [];
        }

        for (var drama in dramaList) {
          if (drama.isCompleted != true) {
            tasksByYear[year]!.add(TaskModel(
              dataModel: dataModel,
              keyword: drama.keyword!,
              drama: drama,
              year: year,
              cityId: dataModel.id!,
              region: dataModel.region!,
            ));
          }
        }
      });
    }

    return tasksByYear;
  }

  // 停止任务
  void onBdStop() {
    if (taskController.taskState == TaskState.running || taskController.taskState == TaskState.error) {
      taskController.setStoppedState();
      logController.addLog('⏹️ 任务已停止');
      update(['buttons']);
      // 停止任务的逻辑
    }
  }

  // 继续任务
  void onBdContinue() async{
    if (taskController.taskState == TaskState.error || taskController.taskState == TaskState.stopped) {
      logController.addLog('✅️ 任务开始继续');
      try {
        logController.addLog("走到这里了吗");
        taskController.taskState = TaskState.running;
        await taskController.distributeTasksToUsers();  // 不需要传参，直接使用类中的 data
      } catch (e) {
        logController.addLog('任务分发失败: $e');
        taskController.taskState = TaskState.error;
      }
      update(['buttons']);

      // 继续任务的逻辑
    }
  }

  Future<bool> checkProxy(String proxyAddress, String proxyPort, [String? username, String? password]) async {
    try {
      final HttpClient client = HttpClient();

      // 设置代理
      client.findProxy = (uri) {
        if (username != null && username.isNotEmpty) {
          return 'PROXY $username:$password@$proxyAddress:$proxyPort';
        }
        return 'PROXY $proxyAddress:$proxyPort';
      };

      // 设置超时
      client.connectionTimeout = Duration(seconds: 10);

      // 测试连接（使用百度作为测试网站）
      final request = await client.getUrl(Uri.parse('https://www.baidu.com'));
      final response = await request.close();

      // 关闭客户端
      client.close();

      // 检查响应状态
      return response.statusCode == 200;
    } catch (e) {
      logController.addLog('代理检测错误: $e');
      logController.addLog("× 代理检测错误:$e");

      return false;
    }
  }

  List<String> generateIndexList(List<dynamic> indexActives) {
    print("indexActives---$indexActives");

    List<String> result = [];

    // 判断每个布尔值，决定是否添加对应的字符串
    if (!indexActives[0]) {
      result.add("全部");
    }
    if (!indexActives[1]) {
      result.add("PC端");
    }
    if (!indexActives[2]) {
      result.add("移动端");
    }
    print("result---$result");

    return result;
  }

// 获取给定年份中的第几周的开始日期
  DateTime getStartOfWeek(DateTime date) {
    return date.subtract(Duration(days: date.weekday - 1));
  }

  int getWeekOfYear(DateTime date) {
    final isoDate = date.toIso8601String().substring(0, 10);
    final weekYear = _getWeekYear(date);
    return int.parse((isoDate.split("-")[1] == '01' && weekYear != date.year
        ? 53 // 跨年周特殊处理
        : (date.difference(DateTime(date.year, 1, 1)).inDays / 7).ceil()) as String);
  }

  // 辅助函数：获取ISO周所在年
  int _getWeekYear(DateTime date) {
    final thursday = date.add(Duration(days: DateTime.thursday - date.weekday));
    return thursday.year;
  }

//  检测按钮
  onCheckExit()async{

    Map<String, String> headers = {
      'Accept': 'application/json, text/plain, */*',
      'Accept-Encoding': 'gzip, deflate, br',
//     'Cipher-Text':"""
// 1719227393545_1719297883463_ru+UprB9gpwT7WwiWAVjCtCYF62iacritHFdi6xzdQoN77ErmFRqYmuQ8xwK2qzOPkkPNx4Kv6Or2YzDFcLMcqk5MXxWGoKIXIzzAMi8mHpVJRCMRswfXd1QWapnnAQsIbw4E2HRHIdBpfH6+uINhE4r5InHgSLkjjioFdGNAMps0ZNoa+S9dJgEzlItL46LBMdZQBeryvasC8GUNlUNlka8wf9uxFTlD/LsOc/qsgn/Uj+lMzTjNqMomYWiXVDqxntXKEW7TVEVva8AXzq4yQzlSsuL9xjFWqTydkoXtVQ6zgYgoB0IIooW+/jgWQQrzNqpOjbbMlXDpkFJDNZ+LnwlvEfSAEWKJTbzEbWvdP8UK3wee9zTUlOWtL7ATPjfDtLQEVt0XLCoGEy/OamDI09GfwkcOG2e/B2bdwtNsDu/N2fa2NZkU3uvObHvAzCt""",
      'Connection': 'close',
      'Host': 'index.baidu.com',
      'Referer': 'https://index.baidu.com/v2/main/index.html',
      'User-Agent':"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.438.400 QQBrowser/13.0.6071.400"
    };

    // 要删除的关键词列表
    List<String> toRemove = [];
    List<String> errorList = [];
    List<String> ppcList = [];

     for(var keyword in keyWords){
       logController.addLog('📍 正在检测关键词: $keyword', );

       logController.addLog("keyword--$keyword");

       String wordSugUrl = "https://index.baidu.com/insight/word/sug";

       if (keyword.contains('+')) {
          var k = keyword.split('+');
          for(var k_i in k){
            logController.addLog(k_i);
            headers['Cookie'] = userController.users[0].cookie;
            var response = await httpClientUtil.post(url: wordSugUrl,headers: headers,data:{"words":[k_i],"source":"pc_home"});
            // 调用函数，检查是否存在 '旅游' 这个 word
            bool exists = checkWordInResult(response, k_i);
            logController.addLog("exists ----- $exists");  // 输出: true
            if (!exists) {
              // 如果没有找到，添加到删除列表
              toRemove.add(keyword);
            }

          }
       }else{
         headers['Cookie'] = userController.users[0].cookie;
         var response = await httpClientUtil.post(url: wordSugUrl,headers: headers,data:{"words":[keyword],"source":"pc_home"});
         logController.addLog("response --- $response");
         var status = response['status'];
         bool exists;
         bool ppcexists;
         if(status != 0){
           // response = await httpClientUtil.post(url: wordSugUrl,headers: headers,data:{"words":[keyword],"source":"pc_home"});
           errorList.add(keyword);
           // exists = checkWordInResult(response, keyword);
           logController.addLog("1");
           continue;

         }else{
           exists = checkWordInResult(response, keyword);
           ppcexists = checkPPcWordInResult(response, keyword);
            logController.addLog("2");

         }
         print(exists);
         print(ppcexists);

         if (exists) {
           toRemove.add(keyword);
         }

         if (ppcexists) {
           // 如果没有找到，添加到删除列表
           ppcList.add(keyword );
         }
       }
     }
    // 删除未找到的关键词
    // keyWords.removeWhere((keyword) => toRemove.contains(keyword));
    // 定义文件路径
    String filePath = 'keywords.txt';
    String ppcfilePath = 'ppckeywords.txt';
    String efilePath = 'errKeywords.txt';

    print(toRemove);
    print(errorList);
    print(ppcList);
    // 创建文件对象
    File file = File(filePath);
    File ppcfile = File(ppcfilePath);
    File efile = File(efilePath);

    // 写入数据到文件
    await file.writeAsString('');  // 先清空文件内容，如果文件已存在
    await ppcfile.writeAsString('');  // 先清空文件内容，如果文件已存在
    await efile.writeAsString('');  // 先清空文件内容，如果文件已存在

    // 将每个关键词写入文件，每个关键词占一行
    for (var keyword in toRemove) {
      await file.writeAsString('$keyword\n', mode: FileMode.append);
    }
    for (var ppckeyword in ppcList) {
      await ppcfile.writeAsString('$ppckeyword\n', mode: FileMode.append);
    }
    for (var ekeyword in errorList) {
      await efile.writeAsString('$ekeyword\n', mode: FileMode.append);
    }

    logController.addLog('数据已写入到 $filePath 文件');
    NotificationUtil.show(
      title: '更新关键词成功',
      body: '已更新所有关键词的数据，''数据已写入到 $filePath 文件',
    );
  }

  bool checkWordInResult(Map<String, dynamic> data, String wordToCheck) {

    // 获取 data 中的 result 列表
    List<dynamic> result = data['data']['result'];

    // 遍历 result 列表
    for (var item in result) {
      // 检查 word 是否匹配
      if (item['word'] == wordToCheck && item['type'] == "word") {
        print("找到匹配的word");
        return true;  // 找到匹配的 word，返回 true
      }
    }

    // 如果遍历完没有找到匹配的 word，返回 false
    return false;
  }

  bool checkPPcWordInResult(Map<String, dynamic> data, String wordToCheck) {

    // 获取 data 中的 result 列表
    List<dynamic> result = data['data']['result'];

    // 遍历 result 列表
    for (var item in result) {
      // 检查 word 是否匹配
      if (item['word'] == wordToCheck && item['type'] == "brand") {
        print("找到匹配的brand");
        return true;  // 找到匹配的 word，返回 true
      }
    }

    // 如果遍历完没有找到匹配的 word，返回 false
    return false;
  }

  // 批量导入代理
  Future<void> importProxies(List<ProxyConfig> proxies, int validTime) async {
    int successCount = 0;
    int failCount = 0;
    
    final selectedUsers = userController.getSelectedUsers();
    if (selectedUsers.isEmpty) {
      showToast("请先选择要设置代理的账号");
      return;
    }
    
    for (int i = 0; i < selectedUsers.length && i < proxies.length; i++) {
      var user = selectedUsers[i];
      if (!user.isStart) {  // 只给未启用的账号设置代理
        bool isValid = await checkProxy(
          proxies[i].ip,
          proxies[i].port,
          proxies[i].username,
          proxies[i].password,
        );
        
        if (isValid) {
          user.proxyAddress = proxies[i].ip;
          user.proxyPort = proxies[i].port;
          user.proxyUsername = proxies[i].username;
          user.proxyPassword = proxies[i].password;
          user.proxyValidTime = validTime;
          user.proxyStartTime = DateTime.now();
          user.isProxy = true;
          successCount++;
        } else {
          failCount++;
        }
      }
    }
    
    update(['list', 'now', 'three']);
    logController.addLog("✅ 批量导入代理完成 - 成功:$successCount 失败:$failCount");
  }

}



