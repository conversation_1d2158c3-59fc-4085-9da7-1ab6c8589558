import 'package:bd/pages/baidu_index/controllers/baidu_index_log_controller%20.dart';
import 'package:bd/pages/baidu_index/controllers/baidu_index_task_controller%20.dart';
import 'package:bd/pages/baidu_index/controllers/baidu_index_user_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tekflat_design/tekflat_design.dart';
import 'package:bd/model/baidu_user_model.dart';
import 'package:bd/utils/toast_util.dart';
import '../baidu_index_logic.dart';

/// 账号列表组件
/// 职责：显示账号列表、单个账号操作、开关控制
class AccountListWidget extends StatelessWidget {
  final BaiduIndexLogic logic;
  final ScrollController scrollController;
  final Function(BuildContext, BaiDuUsers) onProxySettingPressed;

  LogController get logController => Get.find<LogController>();
  UserController get userController => Get.find<UserController>();
  TaskController get taskController => Get.find<TaskController>();


  const AccountListWidget({
    Key? key,
    required this.logic,
    required this.scrollController,
    required this.onProxySettingPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Flexible(
      child: Scrollbar(
        controller: scrollController,
        thumbVisibility: true,
        child: SingleChildScrollView(
          controller: scrollController,
          child: Padding(
            padding: EdgeInsets.all(TekSpacings().mainSpacing),
            child: TekCard(
              width: double.infinity,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  for(var i = 0; i < userController.users.length; i++)
                    _buildAccountItem(context, i),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建单个账号项
  Widget _buildAccountItem(BuildContext context, int index) {
    final user = userController.users[index];
    
    return Container(
      padding: EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      decoration: BoxDecoration(
        color: user.isError ? Colors.red.shade200 : Colors.transparent,
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.shade200,
            width: index < userController.users.length - 1 ? 1 : 0,
          ),
        ),
      ),
      child: Row(
        children: [
          // 复选框
          Checkbox(
            value: user.isSelected,
            onChanged: (value) => userController.toggleUserSelection(index, value ?? false),
          ),
          // 账户信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  user.username,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
                Row(
                  children: [
                    Text(
                      user.time_str,
                      style: TextStyle(
                        color: Colors.grey,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          // 开关控制
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildSwitch(
                "启用",
                user.isStart,
                (value) => _handleEnableSwitch(user, value),
              ),
              _buildSwitch(
                "代理",
                user.isProxy,
                (value) => _handleProxySwitch(user, value),
                enabled: !user.isStart,
              ),
            ],
          ),
          // 代理倒计时显示
          if (user.isProxy &&
              user.proxyStartTime != null &&
              user.proxyValidTime != null)
            _buildProxyTimer(index),
          // 操作按钮
          SizedBox(width: 8),
          _buildActionButtons(context, user, index),
        ],
      ),
    );
  }

  /// 构建开关组件
  Widget _buildSwitch(String label, bool value, Function(bool) onChanged, {bool enabled = true}) {
    return Container(
      margin: EdgeInsets.only(right: 8),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: TextStyle(fontSize: 13),
          ),
          Switch(
            value: value,
            onChanged: enabled ? onChanged : null,
            activeColor: Colors.blue,
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
        ],
      ),
    );
  }

  /// 构建代理倒计时显示
  Widget _buildProxyTimer(int index) {
    return Container(
      margin: EdgeInsets.only(right: 8),
      padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4),
      ),
      child: GetBuilder<BaiduIndexLogic>(
        id: 'proxy_timer_$index',
        builder: (_) {
          final user = userController.users[index];
          DateTime now = DateTime.now();
          DateTime endTime = user.proxyStartTime!.add(
              Duration(minutes: user.proxyValidTime!)
          );
          Duration remaining = endTime.difference(now);

          // 更新倒计时
          if (remaining.isNegative) {
            return Text(
              "已过期",
              style: TextStyle(
                color: Colors.red,
                fontSize: 12,
              ),
            );
          }

          // 格式化剩余时间
          String remainingStr = remaining.inHours > 0
              ? "${remaining.inHours}h${(remaining.inMinutes % 60)}m"
              : "${remaining.inMinutes}m${(remaining.inSeconds % 60)}s";

          return Text(
            remainingStr,
            style: TextStyle(
              color: Colors.blue,
              fontSize: 12,
            ),
          );
        },
      ),
    );
  }

  /// 构建操作按钮组
  Widget _buildActionButtons(BuildContext context, BaiDuUsers user, int index) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        TekButton(
          size: TekButtonSize.small,
          type: TekButtonType.success,
          text: user.username == "暂未登录" ? "登录" : "重新登录",
          onPressed: () => userController.loginAccount(user),
        ),
        SizedBox(width: 8),
        TekButton(
          size: TekButtonSize.small,
          type: TekButtonType.info,
          text: "代理设置",
          onPressed: () => onProxySettingPressed(context, user),
        ),
        SizedBox(width: 8),
        TekButton(
          size: TekButtonSize.small,
          type: TekButtonType.danger,
          text: "删除",
          onPressed: () => _handleDeleteAccount(index),
        ),
      ],
    );
  }

  /// 处理启用开关
  void _handleEnableSwitch(BaiDuUsers user, bool value) {
    // 启用前检查必要信息
    if (value == true) {
      if (user.cookie == null || user.cookie!.isEmpty) {
        showToast("请先登录获取Cookie");
        return;
      }
      if (user.apiKey == null || user.apiKey!.isEmpty) {
        showToast("API Key不能为空");
        return;
      }
      if (user.apiKeyTime == null || user.apiKeyTime!.isEmpty) {
        showToast("API Key时间不能为空");
        return;
      }
      if (user.username == "暂未登录") {
        showToast("请先登录账号");
        return;
      }
    }
    // 通过验证后更新状态
    user.isStart = value;
    logic.update(['list','two']);
  }

  /// 处理代理开关
  void _handleProxySwitch(BaiDuUsers user, bool value) {
    if (!user.isStart) {
      if (value == true) {
        if (user.proxyAddress == null || user.proxyAddress!.isEmpty) {
          showToast("请先设置代理地址");
          return;
        }
        if (user.proxyPort == null || user.proxyPort!.isEmpty) {
          showToast("请先设置代理端口");
          return;
        }
      }
      user.isProxy = value;
      logic.update(['list','two']);
    } else {
      showToast("请先停止账号再设置代理");
    }
  }

  /// 处理删除账号
  void _handleDeleteAccount(int index) {
    userController.users.removeAt(index);
    logic.update(['list', "two"]);
  }
}
