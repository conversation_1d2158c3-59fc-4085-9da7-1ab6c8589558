import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tekflat_design/tekflat_design.dart';
import 'package:bd/utils/python_executor.dart';

/// Python环境检查器组件
class PythonEnvironmentChecker extends StatefulWidget {
  @override
  _PythonEnvironmentCheckerState createState() => _PythonEnvironmentCheckerState();
}

class _PythonEnvironmentCheckerState extends State<PythonEnvironmentChecker> {
  bool _isChecking = false;
  bool _isInstalling = false;
  Map<String, dynamic>? _checkResult;
  List<String> _logs = [];

  @override
  void initState() {
    super.initState();
    _checkEnvironment();
  }

  Future<void> _checkEnvironment() async {
    setState(() {
      _isChecking = true;
      _logs.clear();
    });

    try {
      _addLog('🔍 正在检查Python环境...');
      final result = await PythonExecutor.checkPythonEnvironment();
      
      setState(() {
        _checkResult = result;
        _isChecking = false;
      });

      if (result['pythonAvailable'] == true) {
        _addLog('✅ Python已安装: ${result['pythonVersion']}');
        
        if (result['dependenciesInstalled'] == true) {
          _addLog('✅ 所有依赖包已安装');
        } else {
          _addLog('⚠️ 缺少依赖包: ${result['missingDependencies'].join(', ')}');
        }
      } else {
        _addLog('❌ Python未安装或不在PATH中');
        if (result['error'] != null) {
          _addLog('错误: ${result['error']}');
        }
      }
    } catch (e) {
      _addLog('❌ 检查失败: $e');
      setState(() {
        _isChecking = false;
      });
    }
  }

  Future<void> _installDependencies() async {
    setState(() {
      _isInstalling = true;
    });

    try {
      _addLog('📦 开始安装Python依赖包...');
      final success = await PythonExecutor.installPythonDependencies(
        onOutput: (output) => _addLog(output),
      );

      if (success) {
        _addLog('🎉 依赖包安装完成！');
        // 重新检查环境
        await _checkEnvironment();
      } else {
        _addLog('❌ 依赖包安装失败');
      }
    } catch (e) {
      _addLog('❌ 安装异常: $e');
    } finally {
      setState(() {
        _isInstalling = false;
      });
    }
  }

  void _addLog(String message) {
    setState(() {
      _logs.add('${DateTime.now().toString().substring(11, 19)} $message');
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 600,
      height: 500,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Text(
            'Python环境检查',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16),

          // 环境状态
          if (_checkResult != null) ...[
            _buildStatusCard(),
            SizedBox(height: 16),
          ],

          // 操作按钮
          Row(
            children: [
              TekButton(
                size: TekButtonSize.medium,
                type: TekButtonType.info,
                text: _isChecking ? "检查中..." : "重新检查",
                onPressed: _isChecking ? null : _checkEnvironment,
              ),
              SizedBox(width: 12),
              if (_checkResult?['pythonAvailable'] == true && 
                  _checkResult?['dependenciesInstalled'] == false)
                TekButton(
                  size: TekButtonSize.medium,
                  type: TekButtonType.success,
                  text: _isInstalling ? "安装中..." : "安装依赖",
                  onPressed: _isInstalling ? null : _installDependencies,
                ),
            ],
          ),
          SizedBox(height: 16),

          // 日志输出
          Text(
            '检查日志:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 8),
          Expanded(
            child: Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: ListView.builder(
                itemCount: _logs.length,
                itemBuilder: (context, index) {
                  return Padding(
                    padding: EdgeInsets.symmetric(vertical: 2),
                    child: Text(
                      _logs[index],
                      style: TextStyle(
                        fontSize: 12,
                        fontFamily: 'monospace',
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusCard() {
    final result = _checkResult!;
    final pythonAvailable = result['pythonAvailable'] == true;
    final dependenciesInstalled = result['dependenciesInstalled'] == true;

    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: pythonAvailable && dependenciesInstalled 
              ? Colors.green 
              : Colors.orange,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Python状态
          Row(
            children: [
              Icon(
                pythonAvailable ? Icons.check_circle : Icons.error,
                color: pythonAvailable ? Colors.green : Colors.red,
                size: 20,
              ),
              SizedBox(width: 8),
              Text(
                pythonAvailable 
                    ? 'Python: ${result['pythonVersion']}' 
                    : 'Python: 未安装',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          SizedBox(height: 8),

          // 依赖包状态
          Row(
            children: [
              Icon(
                dependenciesInstalled ? Icons.check_circle : Icons.warning,
                color: dependenciesInstalled ? Colors.green : Colors.orange,
                size: 20,
              ),
              SizedBox(width: 8),
              Text(
                dependenciesInstalled 
                    ? '依赖包: 已安装' 
                    : '依赖包: 缺少 ${result['missingDependencies'].join(', ')}',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),

          // 安装说明
          if (!pythonAvailable) ...[
            SizedBox(height: 12),
            Text(
              '请先安装Python 3.7+，并确保添加到系统PATH中',
              style: TextStyle(
                fontSize: 12,
                color: Colors.red[600],
              ),
            ),
          ],
        ],
      ),
    );
  }
}
