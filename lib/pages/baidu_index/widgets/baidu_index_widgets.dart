import 'package:bd/pages/baidu_index/controllers/baidu_index_task_controller%20.dart';
import 'package:flutter/material.dart';
import 'package:tekflat_design/tekflat_design.dart';
import '../baidu_index_logic.dart';

/// 百度指数页面通用小组件集合
/// 包含虚线组件、计数项组件、文件存储选项组件等

/// 虚线组件
class DashedLine extends StatelessWidget {
  final double dashWidth;
  final double dashSpace;
  final Color color;
  final double height;

  const DashedLine({
    Key? key,
    this.dashWidth = 4.0, // 虚线段的宽度
    this.dashSpace = 2.0, // 虚线段之间的间隔
    this.color = Colors.black, // 虚线的颜色
    this.height = 1.0, // 虚线的高度
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: DashedLinePainter(
        dashWidth: dashWidth,
        dashSpace: dashSpace,
        color: color,
      ),
      child: Container(
        height: height, // 设置虚线的高度
      ),
    );
  }
}

/// 虚线绘制器
class DashedLinePainter extends CustomPainter {
  final double dashWidth;
  final double dashSpace;
  final Color color;

  DashedLinePainter({
    required this.dashWidth,
    required this.dashSpace,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = color
      ..strokeWidth = size.height; // 设置线的宽度

    double startX = 0.0;

    while (startX < size.width) {
      // 画线段
      canvas.drawLine(
        Offset(startX, size.height / 2),
        Offset(startX + dashWidth, size.height / 2),
        paint,
      );

      // 更新起始点
      startX += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false; // 不需要重新绘制
  }
}

/// 计数项构建组件
class CountItem extends StatelessWidget {
  final int count;
  final String label;
  final Color color;

  const CountItem({
    Key? key,
    required this.count,
    required this.label,
    required this.color,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4),
      ),
      child: RichText(
        text: TextSpan(
          children: [
            TextSpan(
              text: count.toString(),
              style: TextStyle(
                color: color,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
            TextSpan(
              text: " $label",
              style: TextStyle(
                color: color,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 文件存储选项组件
class FileStorageOptions extends StatelessWidget {
  final List<String> labels;
  final int selectedIndex;
  final Function(int) onChanged;

  const FileStorageOptions({
    Key? key,
    required this.labels,
    required this.selectedIndex,
    required this.onChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TekTypography(
          text: "文件存放位置",
          type: TekTypographyType.labelBold,
          color: Colors.grey,
        ),
        // SizedBox(height: 6),
        for (int i = 0; i < labels.length; i++) ...[
          SizedBox(
            height: 30,
            child: RadioListTile<int>(
              value: i,
              dense: true,
              groupValue: selectedIndex,
              contentPadding: EdgeInsets.symmetric(horizontal: 0, vertical: -4),
              // 控制内边距
              visualDensity: VisualDensity.compact,
              // 让整体更紧凑
              onChanged: (value) {
                if (value != null) {
                  onChanged(value);
                }
              },
              // contentPadding: EdgeInsets.symmetric(vertical: -10,horizontal: -10), // 控制 RadioListTile 的内边距
              title: Text(labels[i], style: TextStyle(fontSize: 13),),
            ),
          ),
        ],
      ],
    );
  }
}

/// 任务状态工具类
class TaskStateUtils {
  /// 根据状态获取按钮类型
  static TekButtonType getButtonType(TaskState state) {
    switch (state) {
      case TaskState.initial:
        return TekButtonType.info;    // 蓝色
      case TaskState.running:
        return TekButtonType.info;    // 蓝色
      case TaskState.error:
        return TekButtonType.warning; // 黄色
      case TaskState.stopped:
        return TekButtonType.info; // 绿色
    }
  }

  /// 根据状态获取按钮文本
  static String getButtonText(TaskState state) {
    switch (state) {
      case TaskState.initial:
        return "开始";
      case TaskState.running:
        return "进行中";
      case TaskState.error:
        return "继续";
      case TaskState.stopped:
        return "开始";
    }
  }
}
