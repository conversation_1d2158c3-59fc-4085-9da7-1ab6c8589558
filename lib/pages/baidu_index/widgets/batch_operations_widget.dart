import 'package:bd/pages/baidu_index/controllers/baidu_index_log_controller%20.dart';
import 'package:bd/pages/baidu_index/controllers/baidu_index_task_controller%20.dart';
import 'package:bd/pages/baidu_index/controllers/baidu_index_user_controller.dart';
import 'package:bd/pages/baidu_index/controllers/baidu_index_auto_login_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:tekflat_design/tekflat_design.dart';
import 'package:bd/model/baidu_user_model.dart';
import 'package:bd/utils/toast_util.dart';
import '../baidu_index_logic.dart';
import 'package:get/get.dart';
/// 批量操作组件
/// 职责：全选复选框、批量操作按钮、添加账号、批量启用对话框
class BatchOperationsWidget extends StatelessWidget {
  final BaiduIndexLogic logic;
  final Function(BuildContext) onProxyConfigPressed;
  final Function(BuildContext) onBatchProxyPressed;
  LogController get logController => Get.find<LogController>();
  UserController get userController => Get.find<UserController>();
  TaskController get taskController => Get.find<TaskController>();
  const BatchOperationsWidget({
    Key? key,
    required this.logic,
    required this.onProxyConfigPressed,
    required this.onBatchProxyPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: TekSpacings().mainSpacing,
        vertical: 12,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 左侧：全选复选框和标题
          Row(
            children: [
              Checkbox(
                value: userController.isAllSelected,
                onChanged: (value) => userController.toggleAllSelection(value ?? false),
              ),
              TekTypography(
                text: "百度指数账号管理（${userController.users.length}）",
              ),
            ],
          ),
          // 右侧：批量操作按钮组
          Row(
            children: [
              TekButton(
                size: TekButtonSize.small,
                type: TekButtonType.info,
                text: "代理配置",
                onPressed: userController.getSelectedUsers().isEmpty
                    ? null
                    : () => onProxyConfigPressed(context),
              ),
              SizedBox(width: 8),
              TekButton(
                size: TekButtonSize.small,
                type: TekButtonType.info,
                text: "批量设置代理",
                onPressed: userController.getSelectedUsers().isEmpty
                    ? null
                    : () => onBatchProxyPressed(context),
              ),
              SizedBox(width: 8),
              TekButton(
                size: TekButtonSize.small,
                type: TekButtonType.info,
                text: "批量启用",
                onPressed: userController.getSelectedUsers().isEmpty
                    ? null
                    : () => _showBatchEnableDialog(context),
              ),
              SizedBox(width: 8),
              TekButton(
                size: TekButtonSize.small,
                type: TekButtonType.primary,
                text: "自动登录",
                icon: Icon(Icons.auto_mode, size: 16),
                onPressed: () => _showAutoLoginModeDialog(context),
              ),
              SizedBox(width: 8),
              TekButton(
                size: TekButtonSize.small,
                type: TekButtonType.info,
                text: "添加账号",
                onPressed: () => _handleAddAccount(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 显示批量启用对话框
  void _showBatchEnableDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('批量启用账号'),
          content: Text('确定要启用所有选中的账号吗？系统将检查每个账号是否满足启用条件。'),
          actions: <Widget>[
            TextButton(
              child: Text('取消'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: Text('确定'),
              onPressed: () => _handleBatchEnable(context),
            ),
          ],
        );
      },
    );
  }

  /// 处理批量启用
  void _handleBatchEnable(BuildContext context) {
    // 获取所有选中的账号
    List<BaiDuUsers> selectedUsers = userController.getSelectedUsers();
    List<String> errorMessages = [];

    // 检查每个账号并启用
    for (var user in selectedUsers) {
      if (user.isError) {
        errorMessages.add("${user.username}: 账号request block");
        continue;
      }
      if (user.cookie == null || user.cookie!.isEmpty) {
        errorMessages.add("${user.username}: 缺少Cookie");
        continue;
      }
      if (user.apiKey == null || user.apiKey!.isEmpty) {
        errorMessages.add("${user.username}: 缺少API Key");
        continue;
      }
      if (user.apiKeyTime == null || user.apiKeyTime!.isEmpty) {
        errorMessages.add("${user.username}: 缺少API Key时间");
        continue;
      }
      if (user.username == "暂未登录") {
        errorMessages.add("有账号未登录");
        continue;
      }

      // 通过验证，启用账号
      user.isStart = true;
    }

    // 更新UI
    logic.update(['list', 'two']);

    // 关闭对话框
    Navigator.of(context).pop();

    // 显示结果
    if (errorMessages.isEmpty) {
      showToast("所有选中账号已成功启用");
    } else {
      _showErrorDialog(context, errorMessages);
    }
  }

  /// 显示错误信息对话框
  void _showErrorDialog(BuildContext context, List<String> errorMessages) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('部分账号启用失败'),
          content: Container(
            width: 400,
            height: 200,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: errorMessages.map((msg) => Padding(
                  padding: EdgeInsets.only(bottom: 8),
                  child: Text(msg),
                )).toList(),
              ),
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: Text('确定'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  /// 显示自动登录模式选择对话框
  void _showAutoLoginModeDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(Icons.auto_mode, color: Colors.blue, size: 24),
              SizedBox(width: 8),
              Text('选择自动登录模式'),
            ],
          ),
          content: Container(
            width: 400,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '请选择自动登录方式：',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                ),
                SizedBox(height: 16),

                // 模式一：网站验证登录
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.blue.shade200),
                    borderRadius: BorderRadius.circular(8),
                    color: Colors.blue.shade50,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.web, color: Colors.blue.shade600, size: 20),
                          SizedBox(width: 8),
                          Text(
                            '模式一：网站验证登录',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: Colors.blue.shade700,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 8),
                      Text(
                        '通过百度账号验证网站获取可用账号列表，然后批量登录百度指数',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.blue.shade600,
                        ),
                      ),
                    ],
                  ),
                ),

                SizedBox(height: 12),

                // 模式二：Cookie导入登录
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.green.shade200),
                    borderRadius: BorderRadius.circular(8),
                    color: Colors.green.shade50,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.cookie, color: Colors.green.shade600, size: 20),
                          SizedBox(width: 8),
                          Text(
                            '模式二：Cookie导入登录',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: Colors.green.shade700,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 8),
                      Text(
                        '导入Cookie文件，为每个Cookie创建浏览器实例进行登录验证',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.green.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: Text('取消'),
              onPressed: () => Navigator.of(context).pop(),
            ),
            TextButton(
              child: Text('模式一'),
              onPressed: () {
                Navigator.of(context).pop();
                _handleWebsiteAutoLogin(context);
              },
            ),
            TextButton(
              child: Text('模式二'),
              onPressed: () {
                Navigator.of(context).pop();
                _handleCookieAutoLogin(context);
              },
            ),
          ],
        );
      },
    );
  }

  /// 处理网站验证自动登录（模式一）
  void _handleWebsiteAutoLogin(BuildContext context) {
    final TextEditingController urlController = TextEditingController();
    final TextEditingController loginCountController = TextEditingController(text: '5');
    final TextEditingController intervalController = TextEditingController(text: '10');

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(Icons.web, color: Colors.blue, size: 24),
              SizedBox(width: 8),
              Text('网站验证登录'),
            ],
          ),
          content: Container(
            width: 600,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 网站链接输入
                  Text(
                    '百度账号验证网站链接：',
                    style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                  ),
                  SizedBox(height: 8),
                  TextField(
                    controller: urlController,
                    decoration: InputDecoration(
                      hintText: '请输入网站链接，例如：https://example.com',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      prefixIcon: Icon(Icons.link),
                    ),
                  ),
                  SizedBox(height: 16),

                  // 登录配置区域
                  Row(
                    children: [
                      // 登录次数输入
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '登录次数：',
                              style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                            ),
                            SizedBox(height: 8),
                            TextField(
                              controller: loginCountController,
                              keyboardType: TextInputType.number,
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly,
                                LengthLimitingTextInputFormatter(2),
                              ],
                              decoration: InputDecoration(
                                hintText: '1-99',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                prefixIcon: Icon(Icons.numbers),
                                suffixText: '次',
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(width: 16),

                      // 时间间隔输入
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '登录间隔：',
                              style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                            ),
                            SizedBox(height: 8),
                            TextField(
                              controller: intervalController,
                              keyboardType: TextInputType.number,
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly,
                                LengthLimitingTextInputFormatter(3),
                              ],
                              decoration: InputDecoration(
                                hintText: '1-999',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                prefixIcon: Icon(Icons.timer),
                                suffixText: '秒',
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 16),

                  // 功能说明
                  Container(
                    padding: EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue.shade200),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.info_outline, color: Colors.blue.shade600, size: 16),
                            SizedBox(width: 8),
                            Text(
                              '功能说明',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: Colors.blue.shade700,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 8),
                        Text(
                          '• 系统将访问验证网站并提取可用账号数量\n'
                          '• 根据设置的登录次数创建对应数量的浏览器实例\n'
                          '• 每个实例间隔指定时间，避免频繁请求\n'
                          '• 所有实例将访问百度指数页面进行登录',
                          style: TextStyle(
                            fontSize: 11,
                            color: Colors.blue.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: Text('取消'),
              onPressed: () => Navigator.of(context).pop(),
            ),
            TextButton(
              child: Text('开始验证'),
              onPressed: () {
                final url = urlController.text.trim();
                final loginCountText = loginCountController.text.trim();
                final intervalText = intervalController.text.trim();

                // 验证输入
                if (url.isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('请输入网站链接')),
                  );
                  return;
                }

                if (loginCountText.isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('请输入登录次数')),
                  );
                  return;
                }

                if (intervalText.isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('请输入登录间隔')),
                  );
                  return;
                }

                final loginCount = int.tryParse(loginCountText) ?? 0;
                final interval = int.tryParse(intervalText) ?? 0;

                if (loginCount < 1 || loginCount > 99) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('登录次数必须在1-99之间')),
                  );
                  return;
                }

                if (interval < 1 || interval > 999) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('登录间隔必须在1-999秒之间')),
                  );
                  return;
                }

                Navigator.of(context).pop();
                _startWebsiteVerification(url, loginCount, interval);
              },
            ),
          ],
        );
      },
    );
  }

  /// 开始网站验证流程
  Future<void> _startWebsiteVerification(String url, int loginCount, int intervalSeconds) async {
    // 获取或创建自动登录控制器
    final autoLoginController = Get.put(BaiduIndexAutoLoginController());

    // 创建配置
    final config = WebsiteLoginConfig(
      url: url,
      loginCount: loginCount,
      intervalSeconds: intervalSeconds,
    );

    // 执行网站验证登录
    final result = await autoLoginController.executeWebsiteLogin(config);

    if (result.success) {
      logController.addLog('🎉 网站验证登录完成: ${result.message}');
    } else {
      logController.addLog('❌ 网站验证登录失败: ${result.message}');
    }
  }

  /// 处理Cookie导入自动登录（模式二）
  void _handleCookieAutoLogin(BuildContext context) {
    // TODO: 实现Cookie导入登录逻辑
    logController.addLog('🍪 启动Cookie导入自动登录模式');
    logController.addLog('📝 注意：具体登录逻辑将由用户后续补充');

    // 显示提示对话框
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Cookie导入登录'),
          content: Text('Cookie导入登录功能框架已准备就绪。\n\n请提供Cookie文件格式要求和具体验证逻辑，我将为您完善此功能。'),
          actions: <Widget>[
            TextButton(
              child: Text('确定'),
              onPressed: () => Navigator.of(context).pop(),
            ),
          ],
        );
      },
    );
  }

  /// 处理添加账号
  void _handleAddAccount() {
    userController.users.add(BaiDuUsers(
      time_str: logic.formatDateTime(DateTime.now()),
      username: "暂未登录"
    ));
    logic.update(['list', "two"]);
  }
}
