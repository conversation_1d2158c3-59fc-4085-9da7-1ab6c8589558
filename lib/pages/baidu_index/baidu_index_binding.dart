import 'package:bd/pages/baidu_index/controllers/baidu_index_data_controller%20.dart';
import 'package:get/get.dart';

import 'baidu_index_logic.dart';
import 'controllers/baidu_index_log_controller .dart';
import 'controllers/baidu_index_task_controller .dart';
import 'controllers/baidu_index_user_controller.dart';

class BaiduIndexBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => BaiduIndexLogic());
    Get.lazyPut<UserController>(() => UserController());
    Get.lazyPut<LogController>(() => LogController());
    Get.lazyPut<TaskController>(() => TaskController());
    Get.lazyPut<DataController>(() => DataController());
  }
}
