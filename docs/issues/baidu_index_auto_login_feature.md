# 百度指数自动登录功能需求文档

## 项目概述
为百度指数模块的账号管理功能添加自动登录按钮，支持两种自动登录模式：网站验证登录和Cookie导入登录。

## 当前项目结构分析

### 核心文件结构
```
lib/pages/baidu_index/
├── baidu_index_view.dart                    # 主视图页面
├── baidu_index_logic.dart                   # 主逻辑控制器
├── widgets/
│   ├── account_management_dialog.dart       # 账号管理对话框主容器
│   ├── account_list_widget.dart            # 账号列表组件
│   ├── batch_operations_widget.dart        # 批量操作组件
│   ├── config_management_section.dart      # 配置管理区域
│   └── proxy_dialogs_widget.dart           # 代理设置对话框
├── controllers/
│   ├── baidu_index_user_controller.dart    # 用户控制器
│   ├── baidu_index_log_controller.dart     # 日志控制器
│   └── baidu_index_task_controller.dart    # 任务控制器
└── model/
    └── baidu_user_model.dart               # 用户数据模型
```

### 现有登录功能分析
1. **当前登录方式**: 手动点击"登录"按钮，通过Puppeteer打开百度指数页面进行手动登录
2. **登录流程**: 
   - 创建Puppeteer浏览器实例
   - 访问百度指数页面
   - 监听网络请求获取cookie和API密钥
   - 提取用户名验证登录状态
3. **数据存储**: 登录成功后将cookie、apiKey、apiKeyTime、username存储到BaiDuUsers模型中

### 现有UI组件分析
- **账号管理入口**: 在ConfigManagementSection中的"账号管理"按钮
- **账号列表**: AccountListWidget显示账号列表，每个账号有"登录"、"代理设置"、"删除"按钮
- **批量操作**: BatchOperationsWidget提供批量操作功能

## 功能需求

### 1. 自动登录按钮添加
- **位置**: 在账号管理对话框的批量操作区域添加"自动登录"按钮
- **样式**: 使用TekButton组件，type为primary，图标为Icons.auto_mode
- **功能**: 点击后弹出自动登录模式选择对话框

### 2. 自动登录模式选择对话框
- **对话框标题**: "选择自动登录模式"
- **模式选项**:
  - 模式一：网站验证登录
  - 模式二：Cookie导入登录
- **按钮**: 确定、取消

### 3. 模式一：网站验证登录
#### 功能描述
通过Puppeteer访问百度账号验证网站，获取可用账号列表，然后为每个账号创建无痕浏览器实例访问百度指数页面进行登录。

#### 实现步骤
1. 打开百度账号验证网站（URL待用户提供）
2. 获取可用账号列表
3. 为每个账号创建无痕Puppeteer实例
4. 访问百度指数页面：`https://index.baidu.com/v2/main/index.html#/trend/%E5%8D%8E%E4%B8%BA?words=%E5%8D%8E%E4%B8%BA`
5. 执行登录流程（具体逻辑由用户后续补充）

#### 技术要点
- 使用Puppeteer的无痕模式：`browser.createIncognitoBrowserContext()`
- 复用现有的登录监听逻辑
- 批量处理多个账号

### 4. 模式二：Cookie导入登录
#### 功能描述
用户导入Cookie文件，系统为每个Cookie创建Puppeteer实例进行登录验证。

#### 实现步骤
1. 提供文件选择器导入Cookie文件
2. 解析Cookie数据
3. 为每个Cookie创建Puppeteer实例
4. 设置Cookie并访问百度指数页面验证登录状态
5. 保存验证成功的账号信息（具体逻辑由用户后续补充）

#### 技术要点
- 文件选择和读取
- Cookie格式解析
- 批量Cookie验证

## 实现计划

### 阶段一：UI组件开发 ✅
- [x] 在BatchOperationsWidget中添加自动登录按钮
- [x] 创建自动登录模式选择对话框组件
- [x] 集成到账号管理对话框中

### 阶段二：模式一基础框架 ✅
- [x] 创建网站验证登录管理器
- [x] 实现账号获取框架（具体逻辑待补充）
- [x] 实现批量无痕登录框架

### 阶段三：模式二基础框架  
- [ ] 创建Cookie导入登录管理器
- [ ] 实现Cookie文件导入功能
- [ ] 实现批量Cookie验证框架

### 阶段四：集成测试
- [ ] 集成两种模式到主流程
- [ ] 错误处理和用户反馈
- [ ] 功能测试和优化

## 技术实现要点

### 1. 代码组织
- 创建专门的自动登录管理器类
- 复用现有的Puppeteer登录逻辑
- 保持与现有架构的一致性

### 2. 错误处理
- 网络连接异常处理
- 登录失败重试机制
- 用户友好的错误提示

### 3. 性能优化
- 并发登录控制
- 资源释放管理
- 进度显示和取消功能

## 待确认事项
1. 百度账号验证网站的具体URL和数据格式
2. Cookie文件的具体格式要求
3. 自动登录的具体业务逻辑实现细节

## 更新记录
- 2025-01-01: 初始需求文档创建，完成项目结构分析和功能规划
- 2025-01-01: 阶段一完成 - UI组件开发
  - ✅ 在BatchOperationsWidget中添加了"自动登录"按钮（primary类型，带auto_mode图标）
  - ✅ 实现了自动登录模式选择对话框，包含两种模式的详细说明
  - ✅ 添加了模式一和模式二的处理框架方法
  - ✅ 集成了日志输出和用户提示功能
  - 📝 两种模式的具体登录逻辑待用户后续补充
- 2025-01-01: 阶段二完成 - 模式一网站验证登录实现
  - ✅ 添加了URL输入框，支持用户输入百度账号验证网站链接
  - ✅ 实现了完整的网站验证流程，使用Puppeteer打开浏览器访问指定网站
  - ✅ 实现了XPath元素提取功能，自动提取指定位置的次数信息
  - ✅ 添加了次数解析和验证逻辑，支持"15次"格式的文本解析
  - ✅ 集成了详细的日志输出和错误处理
  - ✅ 解决了Puppeteer导入冲突问题（Key类冲突）
  - 📝 编译通过，功能框架完整，可进行实际测试
- 2025-01-01: 模式一功能优化 - 逻辑视图分离和配置增强
  - ✅ 创建了BaiduIndexAutoLoginController（GetxController），符合项目架构规范
  - ✅ 添加了登录次数输入框（1-99次），支持批量登录控制
  - ✅ 添加了登录间隔输入框（1-999秒），避免频繁请求
  - ✅ 优化了UI布局，使用双列输入框和详细的功能说明
  - ✅ 实现了完整的输入验证和错误提示机制
  - ✅ 添加了批量无痕浏览器实例创建功能
  - ✅ 集成了时间间隔控制，支持分批次登录
  - ✅ 重构为GetxController架构，与项目现有模式保持一致
  - 📝 编译通过，架构清晰，功能完整
- 2025-01-01: 模式一完整登录流程实现
  - ✅ 实现了可用次数验证，不足时提醒用户修改登录次数
  - ✅ 实现了完整的单账号登录流程：
    - 访问百度指数页面并等待登录窗口出现
    - 点击二维码登录按钮并等待二维码图片加载
    - 提取二维码图片的src链接
  - ✅ 实现了自动登录处理流程：
    - 访问百度账号验证网站并关闭所有弹窗
    - 点击"二维码链接登录"选项激活登录模式
    - 再次检查并点击可能出现的"不再通知"按钮
    - 点击登录按钮并等待输入框出现
    - 输入二维码链接并点击"立即登录"按钮完成自动登录
  - ✅ 添加了详细的日志输出和错误处理
  - ✅ 支持批量账号处理和时间间隔控制
  - 📝 编译通过，完整登录流程已实现，待添加登录结果监听
- 2025-01-01: 登录结果监听功能完成
  - ✅ 实现了完整的请求拦截机制：
    - 拦截关键JS文件（acs-2057.js）并注入监听代码
    - 使用正则表达式提取动态值（timeKet）
    - 修改JS内容添加window['wrr']和window['wrrtime']
  - ✅ 实现了完整的响应监听机制：
    - 监听百度指数API响应（SearchApi/index）
    - 自动提取Cookie（BDUSS、ab_sr）
    - 获取用户名信息
    - 判断账号状态（正常/被封禁）
  - ✅ 实现了API密钥提取功能：
    - 从window对象提取wrr和wrrtime
    - 自动保存到用户对象中
  - ✅ 集成了用户状态管理：
    - 自动创建BaiDuUsers对象
    - 实时更新用户列表显示
    - 完整的错误状态处理
  - ✅ 添加了弹窗自动关闭功能：
    - 访问验证网站后自动检测弹窗对话框
    - 支持多种类型关闭按钮：`el-dialog__close`和`el-message-box__close`
    - 自动点击"不再通知"按钮，避免重复提示
    - 逐个点击关闭所有弹窗，统计关闭数量，确保页面操作正常
  - 📝 编译通过，完整的自动登录功能已实现
- 2025-01-01: 页面关闭问题修复
  - ✅ 修复了登录过程中页面意外关闭的问题
  - ✅ 添加了"二维码链接登录"选项的重试机制（最多3次）
  - ✅ 优化了错误处理逻辑，即使某个步骤失败也不会关闭页面
  - ✅ 增加了更详细的日志输出，便于调试和监控
  - ✅ 提高了登录流程的稳定性和容错性
- 2025-01-01: 输入框处理优化
  - ✅ 修复了二维码链接无法输入的问题
  - ✅ 添加了输入框焦点获取机制
  - ✅ 使用JavaScript直接设置输入值，确保Vue组件能检测到变化
  - ✅ 触发input和change事件，保证数据绑定正常
  - ✅ 添加了输入验证机制，确认输入是否成功
  - ✅ 优化了输入流程的稳定性和可靠性
  - ✅ 修复了输入框定位错误问题，通过label文本精确定位
  - ✅ 使用JavaScript遍历DOM结构，确保找到正确的输入框
