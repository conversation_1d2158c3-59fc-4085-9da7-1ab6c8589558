# 百度指数自动保存与邮件发送功能需求文档

## 项目概述
为百度指数模块添加任务完成后自动保存数据并压缩发送到客户邮箱的功能。

## 需求分析

### 核心需求
通过勾选框来控制任务完成之后是否自动保存，并且进行压缩发送到发送到客户邮箱

### 功能拆解（根据用户补充修正）
1. **自动保存控制**
   - 添加勾选框控制是否启用自动保存功能
   - 任务完成后根据勾选状态决定是否自动保存数据
   - **使用现有保存按钮相同的逻辑**

2. **文件组织结构**
   - 所有保存生成的文件放在统一的文件夹结构中
   - 格式：`\保存时间\地区\` （如：`\2025-01-15_14-30-25\全国\`）
   - 每个地区文件夹包含该地区的所有CSV文件

3. **数据处理与合并**
   - 保存成功后自动运行Python数据合并脚本
   - 脚本功能：合并同类型CSV文件（按日/周/月/年分类）
   - 生成合并后的截面数据文件

4. **压缩与邮件发送**
   - 将整个保存时间文件夹压缩成ZIP
   - 邮件配置固定在代码中（不需要UI配置）
   - 发送压缩文件到客户邮箱

## 现有代码分析

### 当前数据保存流程
1. **保存触发点**: `details_logs_section.dart` 中的"保存数据"按钮
2. **保存逻辑**: `baidu_index_data_controller.dart` 中的保存方法
   - `handlingData0()`: 全部放在一个文件里
   - `handlingData2()`: 关键词和地区都分开存放
3. **文件存储**: 使用 `StoreUtil.checkAndCreateFolders()` 创建目录结构
4. **数据格式**: CSV格式，支持按日/周/月/年保存

### 需要修改的组件
1. **配置管理区域** (`config_management_section.dart`)
   - 添加自动保存勾选框
   - 添加邮件配置选项

2. **数据控制器** (`baidu_index_data_controller.dart`)
   - 修改保存方法，支持自动保存判断
   - 添加压缩功能
   - 添加邮件发送功能

3. **任务控制器** (`baidu_index_task_controller.dart`)
   - 在任务完成时触发自动保存

## 实现计划（修正版）

### 第一阶段：UI界面扩展
- [ ] 在配置管理区域添加自动保存勾选框
- [ ] 更新BaiduIndexLogic添加自动保存状态变量
- [ ] 不需要邮件配置UI（使用固定配置）

### 第二阶段：文件结构重构
- [ ] 修改StoreUtil，支持按时间戳创建根目录
- [ ] 调整现有保存逻辑，文件保存到 `时间戳/地区/` 结构
- [ ] 确保与现有保存按钮逻辑完全一致

### 第三阶段：Python脚本集成
- [ ] 创建Python数据合并脚本文件
- [ ] 实现Dart调用Python脚本的功能
- [ ] 处理脚本执行的错误和进度反馈

### 第四阶段：压缩与邮件功能
- [ ] 添加ZIP压缩依赖包
- [ ] 实现文件夹压缩功能
- [ ] 添加邮件发送依赖包
- [ ] 实现邮件发送功能（固定配置）

### 第五阶段：自动保存集成
- [ ] 修改任务完成逻辑，集成自动保存判断
- [ ] 实现完整的自动化流程：保存→合并→压缩→发送
- [ ] 添加进度提示和错误处理
- [ ] 测试完整流程

## 技术要点

### 依赖包需求
- `archive`: ZIP文件压缩
- `mailer`: 邮件发送功能

### 配置项设计
```dart
class AutoSaveConfig {
  bool enableAutoSave;           // 是否启用自动保存
  bool enableEmailSend;          // 是否启用邮件发送
  String recipientEmail;         // 收件人邮箱
  String senderEmail;           // 发件人邮箱
  String senderPassword;        // 发件人密码
  String smtpServer;            // SMTP服务器
  int smtpPort;                 // SMTP端口
}
```

### 文件结构（最终版）
```
项目根目录/
├── 2025-01-15_14-30-25/          (保存时间文件夹)
│   ├── 原始数据/                  (新增一层文件夹)
│   │   ├── 全国/
│   │   │   ├── 全国-日-关键词1-日期范围.csv
│   │   │   ├── 全国-周-关键词1-日期范围.csv
│   │   │   ├── 全国-月-关键词1-日期范围.csv
│   │   │   └── 全国-年-关键词1-日期范围.csv
│   │   ├── 北京/
│   │   │   └── *.csv
│   │   └── ...
│   ├── 合并-截面数据/            (Python脚本生成)
│   │   ├── 日-合并_part1.csv
│   │   ├── 周-合并_part1.csv
│   │   ├── 月-合并_part1.csv
│   │   └── 年-合并_part1.csv
│   └── data_package.zip          (最终压缩文件)
```

### Python数据合并脚本集成
- 脚本路径：项目根目录下的 `scripts/merge_csv_data.py`
- 执行时机：数据保存完成后自动调用
- 输入参数：保存时间文件夹路径
- 输出：在保存文件夹内生成 `合并-截面数据` 子目录

## 风险评估
1. **邮件发送安全性**: 需要安全存储邮箱密码
2. **文件大小限制**: 邮件附件大小限制
3. **网络依赖**: 邮件发送需要网络连接
4. **错误处理**: 压缩或发送失败的处理机制

## 进度跟踪
- [x] 需求分析完成
- [x] UI界面设计 - 已添加自动保存勾选框和邮箱输入框
- [x] 文件结构重构 - 支持时间戳/原始数据/地区结构
- [x] Python脚本集成 - 完整的数据合并脚本
- [x] 压缩功能实现 - ZIP压缩工具类
- [x] 邮件发送功能实现 - 邮件发送工具类
- [x] 自动保存集成 - 完整的自动化流程
- [x] 依赖包安装和编译测试 - 编译成功
- [ ] 邮件配置设置
- [ ] Python环境配置
- [ ] 功能测试验证

## 已完成工作
### 2025-01-15 第一阶段：UI界面扩展
- ✅ 在BaiduIndexLogic中添加自动保存状态变量
  - `enableAutoSave`: 控制是否启用自动保存
  - `currentSaveTimestamp`: 记录当前保存的时间戳
  - `recipientEmail`: 收件人邮箱地址
- ✅ 在配置管理区域添加自动保存勾选框
  - 美观的卡片式设计
  - 包含收件人邮箱输入框
  - 响应式显示详细功能说明

### 2025-01-15 第二阶段：文件结构重构
- ✅ 扩展StoreUtil工具类
  - `checkAndCreateTimestampFolders()`: 支持时间戳/原始数据/地区结构
  - `getTimestampRootPath()`: 获取时间戳文件夹根路径
- ✅ 在DataController中添加新的保存方法
  - `handlingDataWithTimestamp()`: 支持时间戳文件夹结构的保存
  - 完全复用现有保存逻辑，确保一致性
- ✅ 在BaiduIndexLogic中添加时间戳生成方法
  - `generateTimestamp()`: 生成格式化的时间戳文件夹名

### 2025-01-15 第三阶段：Python脚本集成
- ✅ 创建Python数据合并脚本
  - 路径: `scripts/merge_csv_data.py`
  - 支持多进程并行处理
  - 智能内存管理和分块保存
  - 完整的错误处理和进度显示

### 2025-01-15 第四阶段：压缩与邮件功能
- ✅ 添加必要的依赖包
  - `archive: ^3.4.10`: ZIP文件压缩
  - `mailer: ^6.1.0`: 邮件发送功能
  - `path_provider: ^2.1.2`: 系统路径获取
- ✅ 创建压缩工具类 (`lib/utils/compression_util.dart`)
  - 支持文件夹递归压缩
  - 进度回调和错误处理
  - 压缩信息统计和验证
- ✅ 创建邮件发送工具类 (`lib/utils/email_util.dart`)
  - 固定邮件配置（根据用户要求）
  - 支持HTML邮件模板
  - 附件发送和进度反馈
- ✅ 创建Python执行工具类 (`lib/utils/python_executor.dart`)
  - 跨平台Python命令查找
  - 实时输出监听和进度解析
  - 依赖包检查和安装

### 2025-01-15 第五阶段：自动保存集成
- ✅ 在DataController中实现完整自动保存流程
  - `executeAutoSaveWorkflow()`: 保存→合并→压缩→发送的完整流程
  - 详细的进度日志和错误处理
  - 自动清理临时文件
- ✅ 在TaskController中集成自动保存触发
  - 任务完成时自动触发自动保存流程
  - 与现有任务状态管理完美集成

### 2025-01-15 编译错误修复与测试
- ✅ 修复压缩工具类编译错误
  - 修复`Future<int>`类型错误
- ✅ 修复邮件工具类编译错误
  - 修复`FileAttachment`构造函数参数错误
  - 修复`SendReport`属性访问错误
- ✅ 依赖包安装成功
  - `archive: ^3.4.10`
  - `mailer: ^6.1.0`
  - `path_provider: ^2.1.2`
- ✅ 项目编译成功
  - Windows Debug版本编译通过
  - 所有新功能集成完毕

### 2025-01-15 运行时错误修复
- ✅ 修复UTF-8编码错误
  - Python脚本输出编码问题修复
  - 使用SystemEncoding()处理进程输出
  - 设置Python环境变量PYTHONIOENCODING=utf-8
- ✅ 优化Python脚本
  - 简化输出信息，减少中文字符
  - 添加Windows平台编码处理
  - 改进错误处理和容错机制
- ✅ 增强自动保存流程
  - Python脚本失败时正确终止流程（根据用户要求）
  - 添加异常捕获和错误过滤
  - 改进日志输出格式
- ✅ 添加Python环境检查功能
  - 创建Python环境检查器组件
  - 支持依赖包自动安装
  - 集成到配置管理界面

## 配置说明

### 邮件配置
需要在 `lib/utils/email_util.dart` 中配置实际的SMTP信息：

```dart
// 替换为实际的邮件配置
static const String _senderEmail = '<EMAIL>';
static const String _senderPassword = 'your-app-password';
static const String _smtpHost = 'smtp.example.com';
static const int _smtpPort = 587;
```

### Python环境配置
确保系统已安装Python并包含以下依赖包：
```bash
pip install pandas tqdm
```

### 使用说明
1. 点击"检查Python环境"按钮，确保Python和依赖包已安装
2. 勾选"任务完成后自动保存并发送邮件"
3. 输入收件人邮箱地址
4. 正常执行百度指数采集任务
5. 任务完成后将自动执行完整流程：保存→合并→压缩→发送

## 备注
- 遵循项目现有的代码规范和架构模式
- 保持向后兼容，不影响现有功能
- 添加适当的错误处理和用户提示
- 编译成功，功能完整集成
