#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
百度指数数据合并脚本
用于合并同类型的CSV文件（按日/周/月/年分类）
"""

import os
import sys
import pandas as pd
from tqdm import tqdm
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, as_completed
import time
from functools import partial

# 设置输出编码为UTF-8
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())


def process_single_file(file_path, chunk_size=100000):
    """
    处理单个CSV文件，返回所有数据块
    优化：增大chunk_size，减少I/O次数
    """
    chunks = []
    try:
        for chunk in pd.read_csv(
            file_path,
            chunksize=chunk_size,
            encoding='utf-8-sig',
            dtype={'日期': 'str', '关键词': 'str'}
        ):
            chunks.append(chunk)
    except Exception as e:
        print(f"文件处理失败: {os.path.basename(file_path)}\n错误类型: {type(e).__name__}")
        return []

    return chunks


def merge_chunks_batch(chunk_list, max_rows=2000000):
    """
    批量合并数据块，减少concat次数
    """
    if not chunk_list:
        return None

    # 一次性合并所有chunks，而不是逐个合并
    try:
        merged_df = pd.concat(chunk_list, ignore_index=True)
        return merged_df
    except MemoryError:
        print("内存不足，降级为分批合并")
        # 如果内存不足，分批合并
        result_df = None
        batch_size = len(chunk_list) // 4  # 分4批处理

        for i in range(0, len(chunk_list), batch_size):
            batch = chunk_list[i:i + batch_size]
            batch_df = pd.concat(batch, ignore_index=True)

            if result_df is None:
                result_df = batch_df
            else:
                result_df = pd.concat([result_df, batch_df], ignore_index=True)

        return result_df


def safe_merge_csv(root_dir):
    """
    安全合并CSV文件的主函数
    
    Args:
        root_dir (str): 时间戳文件夹的根目录路径
    """
    PATTERNS = ['日', '周', '月', '年']
    CHUNK_SIZE = 100000  # 增大chunk size，减少I/O次数
    MAX_WORKERS = min(4, mp.cpu_count())  # 限制并行进程数，避免过度占用资源

    print(f"Processing directory: {root_dir}")

    # 指定子目录名称 - 在原始数据文件夹中查找文件
    raw_data_dir = os.path.join(root_dir, "原始数据")
    if not os.path.exists(raw_data_dir):
        print(f"Error: Raw data directory not found: {raw_data_dir}")
        return False

    # 确保合并输出目录存在
    output_dir = os.path.join(root_dir, "合并-截面数据")
    os.makedirs(output_dir, exist_ok=True)

    success_count = 0
    
    for pattern in PATTERNS:
        print(f"\n{'=' * 30} Processing [{pattern}] pattern {'=' * 30}")
        start_time = time.time()

        # 在原始数据目录中递归查找文件
        csv_files = [
            os.path.join(root, file)
            for root, _, files in os.walk(raw_data_dir)
            for file in files
            if f"-{pattern}-" in file and file.lower().endswith(".csv")
        ]

        if not csv_files:
            print(f"No {pattern} pattern files found")
            continue

        print(f"Found {len(csv_files)} files, starting parallel processing...")

        # 并行处理所有文件
        all_chunks = []
        process_func = partial(process_single_file, chunk_size=CHUNK_SIZE)

        with ProcessPoolExecutor(max_workers=MAX_WORKERS) as executor:
            # 提交所有任务
            future_to_file = {
                executor.submit(process_func, file): file
                for file in csv_files
            }

            # 收集结果
            for future in tqdm(as_completed(future_to_file),
                             total=len(csv_files),
                             desc='文件处理进度'):
                file_path = future_to_file[future]
                try:
                    chunks = future.result()
                    all_chunks.extend(chunks)
                except Exception as e:
                    print(f"处理文件失败 {os.path.basename(file_path)}: {e}")

        if not all_chunks:
            print(f"没有成功处理的数据块")
            continue

        print(f"共收集到 {len(all_chunks)} 个数据块，开始智能合并...")

        # 分块保存逻辑（保持100万行限制）
        chunk_counter = 1
        total_rows = 0

        def save_chunk(df, counter):
            output_path = os.path.join(output_dir, f"{pattern}-合并_part{counter}.csv")
            df.to_csv(output_path, index=False, encoding='utf-8-sig')
            print(f"已保存分块 {counter} ({len(df):,}行) -> {os.path.basename(output_path)}")
            return counter + 1

        # 优化：智能批量合并策略
        current_batch = []
        current_rows = 0

        for i, chunk in enumerate(tqdm(all_chunks, desc='智能合并进度')):
            current_batch.append(chunk)
            current_rows += len(chunk)

            # 当行数接近100万或者是最后一批时，进行合并保存
            if current_rows >= 950000 or i == len(all_chunks) - 1:

                if current_batch:
                    # 批量合并当前批次
                    batch_df = pd.concat(current_batch, ignore_index=True)

                    # 按100万行分割保存
                    rows_per_part = 1000000
                    if len(batch_df) <= rows_per_part:
                        # 直接保存
                        chunk_counter = save_chunk(batch_df, chunk_counter)
                        total_rows += len(batch_df)
                    else:
                        # 分割保存
                        for start_idx in range(0, len(batch_df), rows_per_part):
                            end_idx = min(start_idx + rows_per_part, len(batch_df))
                            part_df = batch_df.iloc[start_idx:end_idx].copy()
                            chunk_counter = save_chunk(part_df, chunk_counter)
                            total_rows += len(part_df)

                # 重置批次
                current_batch = []
                current_rows = 0

        elapsed_time = time.time() - start_time
        print(f"✅ {pattern}模式处理完成!")
        print(f"   📊 总行数: {total_rows:,}")
        print(f"   📁 输出文件: {chunk_counter-1} 个")
        print(f"   ⏱️  耗时: {elapsed_time:.2f}秒")
        
        success_count += 1

    print(f"\n🎉 数据合并完成！成功处理 {success_count}/{len(PATTERNS)} 种模式")
    return success_count > 0


if __name__ == "__main__":
    # 设置多进程启动方式（Windows需要）
    if hasattr(mp, 'set_start_method'):
        try:
            mp.set_start_method('spawn', force=True)
        except RuntimeError:
            pass  # 已经设置过了
    
    # 从命令行参数获取根目录路径
    if len(sys.argv) != 2:
        print("用法: python merge_csv_data.py <时间戳文件夹路径>")
        sys.exit(1)
    
    root_directory = sys.argv[1]
    
    if not os.path.exists(root_directory):
        print(f"错误：目录不存在: {root_directory}")
        sys.exit(1)
    
    # 执行合并
    success = safe_merge_csv(root_directory)
    sys.exit(0 if success else 1)
